<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2016 ACSONE SA/NV
     License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo>
    <record id="res_users_report_py3o" model="ir.actions.report">
        <field name="name">Py3o Demo Report</field>
        <field name="type">ir.actions.report</field>
        <field name="model">res.users</field>
        <field name="report_name">py3o_user_info</field>
        <field name="report_type">py3o</field>
        <field name="py3o_filetype">odt</field>
        <field name="module">report_py3o</field>
        <field name="py3o_template_fallback">demo/res_user.odt</field>
        <field
            name="print_report_name"
        >object.name.replace(' ', '_') + '-demo.odt'</field>
        <field name="binding_model_id" ref="base.model_res_users" />
        <field name="binding_type">report</field>
    </record>
</odoo>
