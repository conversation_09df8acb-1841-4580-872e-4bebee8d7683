# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * report_py3o
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-01-16 08:52+0000\n"
"PO-Revision-Date: 2018-01-16 08:52+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2017\n"
"Language-Team: Hebrew (https://www.transifex.com/oca/teams/23907/he/)\n"
"Language: he\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: report_py3o
#. odoo-python
#: code:addons/report_py3o/models/ir_actions_report.py:0
#, python-format
msgid "(Native)"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_report__create_uid
#: model:ir.model.fields,field_description:report_py3o.field_py3o_template__create_uid
msgid "Created by"
msgstr "נוצר על ידי"

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_report__create_date
#: model:ir.model.fields,field_description:report_py3o.field_py3o_template__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_report__display_name
#: model:ir.model.fields,field_description:report_py3o.field_py3o_template__display_name
msgid "Display Name"
msgstr "השם המוצג"

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_ir_actions_report__py3o_template_fallback
msgid "Fallback"
msgstr ""

#. module: report_py3o
#. odoo-python
#: code:addons/report_py3o/models/ir_actions_report.py:0
#, python-format
msgid "Field 'Output Format' is required for Py3O report"
msgstr ""

#. module: report_py3o
#: model_terms:ir.ui.view,arch_db:report_py3o.py3o_template_configuration_search_view
msgid "File Type"
msgstr ""

#. module: report_py3o
#: model_terms:ir.ui.view,arch_db:report_py3o.py3o_template_configuration_search_view
msgid "Group By"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_report__id
#: model:ir.model.fields,field_description:report_py3o.field_py3o_template__id
msgid "ID"
msgstr "מזהה"

#. module: report_py3o
#: model:ir.model.fields,help:report_py3o.field_ir_actions_report__py3o_template_fallback
msgid ""
"If the user does not provide a template this will be used it should be a "
"relative path to root of YOUR module or an absolute path on your server."
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,help:report_py3o.field_ir_actions_report__py3o_multi_in_one
msgid ""
"If you execute a report on several records, by default Odoo will generate a "
"ZIP file that contains as many files as selected records. If you enable this "
"option, Odoo will generate instead a single report for the selected records."
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_report__ir_actions_report_id
msgid "Ir Actions Report"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_ir_actions_report__is_py3o_native_format
msgid "Is Py3O Native Format"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_ir_actions_report__is_py3o_report_not_available
msgid "Is Py3O Report Not Available"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_report____last_update
#: model:ir.model.fields,field_description:report_py3o.field_py3o_template____last_update
msgid "Last Modified on"
msgstr "תאריך שינוי אחרון"

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_report__write_uid
#: model:ir.model.fields,field_description:report_py3o.field_py3o_template__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על ידי"

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_report__write_date
#: model:ir.model.fields,field_description:report_py3o.field_py3o_template__write_date
msgid "Last Updated on"
msgstr "עודכן לאחרונה על"

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_template__py3o_template_data
#: model_terms:ir.ui.view,arch_db:report_py3o.py3o_report_view
msgid "LibreOffice Template"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_template__filetype
msgid "LibreOffice Template File Type"
msgstr ""

#. module: report_py3o
#. odoo-python
#: code:addons/report_py3o/models/py3o_report.py:0
#, python-format
msgid "Libreoffice runtime not available. Please contact your administrator."
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_ir_actions_report__module
msgid "Module"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_ir_actions_report__msg_py3o_report_not_available
msgid "Msg Py3O Report Not Available"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_ir_actions_report__py3o_multi_in_one
msgid "Multiple Records in a Single Report"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_py3o_template__name
msgid "Name"
msgstr "שם"

#. module: report_py3o
#. odoo-python
#: code:addons/report_py3o/models/py3o_report.py:0
#, python-format
msgid "No template found. Aborting."
msgstr ""

#. module: report_py3o
#: model:ir.model.fields.selection,name:report_py3o.selection__py3o_template__filetype__odp
msgid "ODF Presentation"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields.selection,name:report_py3o.selection__py3o_template__filetype__fodp
msgid "ODF Presentation (Flat)"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields.selection,name:report_py3o.selection__py3o_template__filetype__ods
msgid "ODF Spreadsheet"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields.selection,name:report_py3o.selection__py3o_template__filetype__fods
msgid "ODF Spreadsheet (Flat)"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields.selection,name:report_py3o.selection__py3o_template__filetype__odt
msgid "ODF Text Document"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields.selection,name:report_py3o.selection__py3o_template__filetype__fodt
msgid "ODF Text Document (Flat)"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_ir_actions_report__py3o_filetype
msgid "Output Format"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_ir_actions_report__lo_bin_path
msgid "Path to the libreoffice runtime"
msgstr ""

#. module: report_py3o
#: model:ir.actions.report,name:report_py3o.res_users_report_py3o
msgid "Py3o Demo Report"
msgstr ""

#. module: report_py3o
#: model_terms:ir.ui.view,arch_db:report_py3o.act_report_xml_search_view
msgid "Py3o Reports"
msgstr ""

#. module: report_py3o
#: model:ir.actions.act_window,name:report_py3o.py3o_template_configuration_action
#: model:ir.ui.menu,name:report_py3o.py3o_template_configuration_menu
msgid "Py3o Templates"
msgstr ""

#. module: report_py3o
#: model:ir.model,name:report_py3o.model_py3o_template
msgid "Py3o template"
msgstr ""

#. module: report_py3o
#: model:ir.model,name:report_py3o.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: report_py3o
#: model:ir.model,name:report_py3o.model_py3o_report
msgid "Report Py30"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_ir_actions_report__report_type
msgid "Report Type"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,field_description:report_py3o.field_ir_actions_report__py3o_template_id
msgid "Template"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,help:report_py3o.field_ir_actions_report__module
msgid "The implementer module that provides this report"
msgstr ""

#. module: report_py3o
#. odoo-python
#: code:addons/report_py3o/models/ir_actions_report.py:0
#, python-format
msgid ""
"The libreoffice runtime is required to genereate the py3o report '%s' but is "
"not found into the bin path. You must install the libreoffice runtime on the "
"server. If the runtime is already installed and is not found by Odoo, you "
"can provide the full path to the runtime by setting the key 'py3o."
"conversion_command' into the configuration parameters."
msgstr ""

#. module: report_py3o
#: model:ir.model.fields,help:report_py3o.field_ir_actions_report__report_type
msgid ""
"The type of the report that will be rendered, each one having its own "
"rendering method. HTML means the report will be opened directly in your "
"browser PDF means the report will be rendered using Wkhtmltopdf and "
"downloaded by the user."
msgstr ""

#. module: report_py3o
#: model:ir.actions.report,print_report_name:report_py3o.res_users_report_py3o
msgid "object.name.replace(' ', '_') + '-demo.odt'"
msgstr ""

#. module: report_py3o
#: model:ir.model.fields.selection,name:report_py3o.selection__ir_actions_report__report_type__py3o
msgid "py3o"
msgstr ""
