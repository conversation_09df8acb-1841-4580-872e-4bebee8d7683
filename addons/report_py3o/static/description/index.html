<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="generator" content="Docutils: https://docutils.sourceforge.io/" />
<title>Py3o Report Engine</title>
<style type="text/css">

/*
:Author: <PERSON> (<EMAIL>)
:Id: $Id: html4css1.css 8954 2022-01-20 10:10:25Z milde $
:Copyright: This stylesheet has been placed in the public domain.

Default cascading style sheet for the HTML output of Docutils.

See https://docutils.sourceforge.io/docs/howto/html-stylesheets.html for how to
customize this style sheet.
*/

/* used to remove borders from tables and images */
.borderless, table.borderless td, table.borderless th {
  border: 0 }

table.borderless td, table.borderless th {
  /* Override padding for "table.docutils td" with "! important".
     The right padding separates the table cells. */
  padding: 0 0.5em 0 0 ! important }

.first {
  /* Override more specific margin styles with "! important". */
  margin-top: 0 ! important }

.last, .with-subtitle {
  margin-bottom: 0 ! important }

.hidden {
  display: none }

.subscript {
  vertical-align: sub;
  font-size: smaller }

.superscript {
  vertical-align: super;
  font-size: smaller }

a.toc-backref {
  text-decoration: none ;
  color: black }

blockquote.epigraph {
  margin: 2em 5em ; }

dl.docutils dd {
  margin-bottom: 0.5em }

object[type="image/svg+xml"], object[type="application/x-shockwave-flash"] {
  overflow: hidden;
}

/* Uncomment (and remove this text!) to get bold-faced definition list terms
dl.docutils dt {
  font-weight: bold }
*/

div.abstract {
  margin: 2em 5em }

div.abstract p.topic-title {
  font-weight: bold ;
  text-align: center }

div.admonition, div.attention, div.caution, div.danger, div.error,
div.hint, div.important, div.note, div.tip, div.warning {
  margin: 2em ;
  border: medium outset ;
  padding: 1em }

div.admonition p.admonition-title, div.hint p.admonition-title,
div.important p.admonition-title, div.note p.admonition-title,
div.tip p.admonition-title {
  font-weight: bold ;
  font-family: sans-serif }

div.attention p.admonition-title, div.caution p.admonition-title,
div.danger p.admonition-title, div.error p.admonition-title,
div.warning p.admonition-title, .code .error {
  color: red ;
  font-weight: bold ;
  font-family: sans-serif }

/* Uncomment (and remove this text!) to get reduced vertical space in
   compound paragraphs.
div.compound .compound-first, div.compound .compound-middle {
  margin-bottom: 0.5em }

div.compound .compound-last, div.compound .compound-middle {
  margin-top: 0.5em }
*/

div.dedication {
  margin: 2em 5em ;
  text-align: center ;
  font-style: italic }

div.dedication p.topic-title {
  font-weight: bold ;
  font-style: normal }

div.figure {
  margin-left: 2em ;
  margin-right: 2em }

div.footer, div.header {
  clear: both;
  font-size: smaller }

div.line-block {
  display: block ;
  margin-top: 1em ;
  margin-bottom: 1em }

div.line-block div.line-block {
  margin-top: 0 ;
  margin-bottom: 0 ;
  margin-left: 1.5em }

div.sidebar {
  margin: 0 0 0.5em 1em ;
  border: medium outset ;
  padding: 1em ;
  background-color: #ffffee ;
  width: 40% ;
  float: right ;
  clear: right }

div.sidebar p.rubric {
  font-family: sans-serif ;
  font-size: medium }

div.system-messages {
  margin: 5em }

div.system-messages h1 {
  color: red }

div.system-message {
  border: medium outset ;
  padding: 1em }

div.system-message p.system-message-title {
  color: red ;
  font-weight: bold }

div.topic {
  margin: 2em }

h1.section-subtitle, h2.section-subtitle, h3.section-subtitle,
h4.section-subtitle, h5.section-subtitle, h6.section-subtitle {
  margin-top: 0.4em }

h1.title {
  text-align: center }

h2.subtitle {
  text-align: center }

hr.docutils {
  width: 75% }

img.align-left, .figure.align-left, object.align-left, table.align-left {
  clear: left ;
  float: left ;
  margin-right: 1em }

img.align-right, .figure.align-right, object.align-right, table.align-right {
  clear: right ;
  float: right ;
  margin-left: 1em }

img.align-center, .figure.align-center, object.align-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

table.align-center {
  margin-left: auto;
  margin-right: auto;
}

.align-left {
  text-align: left }

.align-center {
  clear: both ;
  text-align: center }

.align-right {
  text-align: right }

/* reset inner alignment in figures */
div.align-right {
  text-align: inherit }

/* div.align-center * { */
/*   text-align: left } */

.align-top    {
  vertical-align: top }

.align-middle {
  vertical-align: middle }

.align-bottom {
  vertical-align: bottom }

ol.simple, ul.simple {
  margin-bottom: 1em }

ol.arabic {
  list-style: decimal }

ol.loweralpha {
  list-style: lower-alpha }

ol.upperalpha {
  list-style: upper-alpha }

ol.lowerroman {
  list-style: lower-roman }

ol.upperroman {
  list-style: upper-roman }

p.attribution {
  text-align: right ;
  margin-left: 50% }

p.caption {
  font-style: italic }

p.credits {
  font-style: italic ;
  font-size: smaller }

p.label {
  white-space: nowrap }

p.rubric {
  font-weight: bold ;
  font-size: larger ;
  color: maroon ;
  text-align: center }

p.sidebar-title {
  font-family: sans-serif ;
  font-weight: bold ;
  font-size: larger }

p.sidebar-subtitle {
  font-family: sans-serif ;
  font-weight: bold }

p.topic-title {
  font-weight: bold }

pre.address {
  margin-bottom: 0 ;
  margin-top: 0 ;
  font: inherit }

pre.literal-block, pre.doctest-block, pre.math, pre.code {
  margin-left: 2em ;
  margin-right: 2em }

pre.code .ln { color: grey; } /* line numbers */
pre.code, code { background-color: #eeeeee }
pre.code .comment, code .comment { color: #5C6576 }
pre.code .keyword, code .keyword { color: #3B0D06; font-weight: bold }
pre.code .literal.string, code .literal.string { color: #0C5404 }
pre.code .name.builtin, code .name.builtin { color: #352B84 }
pre.code .deleted, code .deleted { background-color: #DEB0A1}
pre.code .inserted, code .inserted { background-color: #A3D289}

span.classifier {
  font-family: sans-serif ;
  font-style: oblique }

span.classifier-delimiter {
  font-family: sans-serif ;
  font-weight: bold }

span.interpreted {
  font-family: sans-serif }

span.option {
  white-space: nowrap }

span.pre {
  white-space: pre }

span.problematic {
  color: red }

span.section-subtitle {
  /* font-size relative to parent (h1..h6 element) */
  font-size: 80% }

table.citation {
  border-left: solid 1px gray;
  margin-left: 1px }

table.docinfo {
  margin: 2em 4em }

table.docutils {
  margin-top: 0.5em ;
  margin-bottom: 0.5em }

table.footnote {
  border-left: solid 1px black;
  margin-left: 1px }

table.docutils td, table.docutils th,
table.docinfo td, table.docinfo th {
  padding-left: 0.5em ;
  padding-right: 0.5em ;
  vertical-align: top }

table.docutils th.field-name, table.docinfo th.docinfo-name {
  font-weight: bold ;
  text-align: left ;
  white-space: nowrap ;
  padding-left: 0 }

/* "booktabs" style (no vertical lines) */
table.docutils.booktabs {
  border: 0px;
  border-top: 2px solid;
  border-bottom: 2px solid;
  border-collapse: collapse;
}
table.docutils.booktabs * {
  border: 0px;
}
table.docutils.booktabs th {
  border-bottom: thin solid;
  text-align: left;
}

h1 tt.docutils, h2 tt.docutils, h3 tt.docutils,
h4 tt.docutils, h5 tt.docutils, h6 tt.docutils {
  font-size: 100% }

ul.auto-toc {
  list-style-type: none }

</style>
</head>
<body>
<div class="document" id="py3o-report-engine">
<h1 class="title">Py3o Report Engine</h1>

<!-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!! This file is generated by oca-gen-addon-readme !!
!! changes will be overwritten.                   !!
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!! source digest: sha256:55201dffaad8eabd307cbe639210cae71b8e0c83e192a8fd9723d5ce235bf51f
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! -->
<p><a class="reference external image-reference" href="https://odoo-community.org/page/development-status"><img alt="Beta" src="https://img.shields.io/badge/maturity-Beta-yellow.png" /></a> <a class="reference external image-reference" href="http://www.gnu.org/licenses/agpl-3.0-standalone.html"><img alt="License: AGPL-3" src="https://img.shields.io/badge/licence-AGPL--3-blue.png" /></a> <a class="reference external image-reference" href="https://github.com/OCA/reporting-engine/tree/16.0/report_py3o"><img alt="OCA/reporting-engine" src="https://img.shields.io/badge/github-OCA%2Freporting--engine-lightgray.png?logo=github" /></a> <a class="reference external image-reference" href="https://translation.odoo-community.org/projects/reporting-engine-16-0/reporting-engine-16-0-report_py3o"><img alt="Translate me on Weblate" src="https://img.shields.io/badge/weblate-Translate%20me-F47D42.png" /></a> <a class="reference external image-reference" href="https://runboat.odoo-community.org/builds?repo=OCA/reporting-engine&amp;target_branch=16.0"><img alt="Try me on Runboat" src="https://img.shields.io/badge/runboat-Try%20me-875A7B.png" /></a></p>
<p>The py3o reporting engine is a reporting engine for Odoo based on <a class="reference external" href="http://www.libreoffice.org/">Libreoffice</a>:</p>
<ul class="simple">
<li>the report is created with Libreoffice (ODT or ODS),</li>
<li>the report is stored on the server in OpenDocument format (.odt or .ods file)</li>
<li>the report is sent to the user in OpenDocument format or in any output format supported by Libreoffice (PDF, HTML, DOC, DOCX, Docbook, XLS, etc.)</li>
</ul>
<p>The key advantages of a Libreoffice based reporting engine are:</p>
<ul class="simple">
<li>no need to be a developer to create or modify a report: the report is created and modified with Libreoffice. So this reporting engine has a full WYSIWYG report development tool!</li>
<li>For a PDF report in A4/Letter format, it’s easier to develop it with a tool such as Libreoffice that is designed to create A4/Letter documents than to develop it in HTML/CSS, also some print peculiarities (backgrounds, margin boxes) are not very well supported by the HTML/CSS based solutions.</li>
<li>If you want your users to be able to modify the document after its generation by Odoo, just configure the document with ODT output (or DOC or DOCX) and the user will be able to modify the document with Libreoffice (or Word) after its generation by Odoo.</li>
<li>Easy development of spreadsheet reports in ODS format (XLS output possible).</li>
</ul>
<p>This module <em>report_py3o</em> is the base module for the Py3o reporting engine. If used alone, it will spawn a libreoffice process for each ODT to PDF (or ODT to DOCX, ..) document conversion. This is slow and can become a problem if you have a lot of reports to convert from ODT to another format. In this case, you should consider the additionnal module <em>report_py3o_fusion_server</em> which is designed to work with a libreoffice daemon. With <em>report_py3o_fusion_server</em>, the technical environnement is more complex to setup because you have to install additionnal software components and run 2 daemons, but you have much better performances and you can configure the libreoffice PDF export options in Odoo (allows to generate PDF forms, PDF/A documents, password-protected PDFs, watermarked PDFs, etc.).</p>
<p>This reporting engine is an alternative to <a class="reference external" href="https://github.com/aeroo-community/aeroo_reports">Aeroo</a>: these two reporting engines have similar features but their implementation is entirely different. You cannot use aeroo templates as drop in replacement though, you’ll have to change a few details.</p>
<p><strong>Table of contents</strong></p>
<div class="contents local topic" id="contents">
<ul class="simple">
<li><a class="reference internal" href="#installation" id="toc-entry-1">Installation</a></li>
<li><a class="reference internal" href="#configuration" id="toc-entry-2">Configuration</a><ul>
<li><a class="reference internal" href="#configuration-parameters" id="toc-entry-3">Configuration parameters</a></li>
</ul>
</li>
<li><a class="reference internal" href="#usage" id="toc-entry-4">Usage</a><ul>
<li><a class="reference internal" href="#available-functions-and-objects" id="toc-entry-5">Available functions and objects</a></li>
<li><a class="reference internal" href="#sample-report-templates" id="toc-entry-6">Sample report templates</a></li>
</ul>
</li>
<li><a class="reference internal" href="#known-issues-roadmap" id="toc-entry-7">Known issues / Roadmap</a></li>
<li><a class="reference internal" href="#bug-tracker" id="toc-entry-8">Bug Tracker</a></li>
<li><a class="reference internal" href="#credits" id="toc-entry-9">Credits</a><ul>
<li><a class="reference internal" href="#authors" id="toc-entry-10">Authors</a></li>
<li><a class="reference internal" href="#contributors" id="toc-entry-11">Contributors</a></li>
<li><a class="reference internal" href="#maintainers" id="toc-entry-12">Maintainers</a></li>
</ul>
</li>
</ul>
</div>
<div class="section" id="installation">
<h1><a class="toc-backref" href="#toc-entry-1">Installation</a></h1>
<p>Install the required python libs:</p>
<pre class="code literal-block">
pip install py3o.template
pip install py3o.formats
</pre>
<p>To allow the conversion of ODT or ODS reports to other formats (PDF, DOC, DOCX, etc.), install libreoffice:</p>
<pre class="code literal-block">
apt-get --no-install-recommends install libreoffice
</pre>
</div>
<div class="section" id="configuration">
<h1><a class="toc-backref" href="#toc-entry-2">Configuration</a></h1>
<p>For example, to replace the native invoice report by a custom py3o report, add the following XML file in your custom module:</p>
<pre class="code literal-block">
&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;
&lt;odoo&gt;

&lt;record id=&quot;account.account_invoices&quot; model=&quot;ir.actions.report&quot;&gt;
    &lt;field name=&quot;report_type&quot;&gt;py3o&lt;/field&gt;
    &lt;field name=&quot;py3o_filetype&quot;&gt;odt&lt;/field&gt;
    &lt;field name=&quot;module&quot;&gt;my_custom_module_base&lt;/field&gt;
    &lt;field name=&quot;py3o_template_fallback&quot;&gt;report/account_invoice.odt&lt;/field&gt;
&lt;/record&gt;

&lt;/odoo&gt;
</pre>
<p>where <em>my_custom_module_base</em> is the name of the custom Odoo module. In this example, the invoice ODT file is located in <em>my_custom_module_base/report/account_invoice.odt</em>.</p>
<p>It’s also possible to reference a template located in a trusted path of your
Odoo server. In this case you must let the <em>module</em> entry empty and specify
the path to the template as <em>py3o_template_fallback</em>.</p>
<pre class="code literal-block">
&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;
&lt;odoo&gt;

&lt;record id=&quot;account.account_invoices&quot; model=&quot;ir.actions.report&quot;&gt;
    &lt;field name=&quot;report_type&quot;&gt;py3o&lt;/field&gt;
    &lt;field name=&quot;py3o_filetype&quot;&gt;odt&lt;/field&gt;
    &lt;field name=&quot;py3o_template_fallback&quot;&gt;/odoo/templates/py3o/report/account_invoice.odt&lt;/field&gt;
&lt;/record&gt;

&lt;/odoo&gt;
</pre>
<p>Moreover, you must also modify the Odoo server configuration file to declare
the allowed root directory for your py3o templates. Only templates located
into this directory can be loaded by py3o report.</p>
<pre class="code literal-block">
[options]
...

[report_py3o]
root_tmpl_path=/odoo/templates/py3o
</pre>
<p>If you want an invoice in PDF format instead of ODT format, the XML file should look like:</p>
<pre class="code literal-block">
&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;
&lt;odoo&gt;

&lt;record id=&quot;account.account_invoices&quot; model=&quot;ir.actions.report&quot;&gt;
    &lt;field name=&quot;report_type&quot;&gt;py3o&lt;/field&gt;
    &lt;field name=&quot;py3o_filetype&quot;&gt;pdf&lt;/field&gt;
    &lt;field name=&quot;module&quot;&gt;my_custom_module_base&lt;/field&gt;
    &lt;field name=&quot;py3o_template_fallback&quot;&gt;report/account_invoice.odt&lt;/field&gt;
&lt;/record&gt;

&lt;/odoo&gt;
</pre>
<p>If you want to add a new py3o PDF report (and not replace a native report), the XML file should look like this:</p>
<pre class="code literal-block">
&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;
&lt;odoo&gt;

&lt;record id=&quot;partner_summary_report&quot; model=&quot;ir.actions.report&quot;&gt;
    &lt;field name=&quot;name&quot;&gt;Partner Summary&lt;/field&gt;
    &lt;field name=&quot;model&quot;&gt;res.partner&lt;/field&gt;
    &lt;field name=&quot;report_name&quot;&gt;res.partner.summary&lt;/field&gt;
    &lt;field name=&quot;report_type&quot;&gt;py3o&lt;/field&gt;
    &lt;field name=&quot;py3o_filetype&quot;&gt;pdf&lt;/field&gt;
    &lt;field name=&quot;module&quot;&gt;my_custom_module_base&lt;/field&gt;
    &lt;field name=&quot;py3o_template_fallback&quot;&gt;report/partner_summary.odt&lt;/field&gt;
    &lt;!-- Add entry in &quot;Print&quot; drop-down list --&gt;
    &lt;field name=&quot;binding_type&quot;&gt;report&lt;/field&gt;
    &lt;field name=&quot;binding_model_id&quot; ref=&quot;base.model_res_partner&quot;/&gt;
&lt;/record&gt;

&lt;/odoo&gt;
</pre>
<div class="section" id="configuration-parameters">
<h2><a class="toc-backref" href="#toc-entry-3">Configuration parameters</a></h2>
<dl class="docutils">
<dt>py3o.conversion_command</dt>
<dd>The command to be used to run the conversion, <tt class="docutils literal">libreoffice</tt> by default. If you change this, whatever you set here must accept the parameters <tt class="docutils literal"><span class="pre">--headless</span> <span class="pre">--convert-to</span> $ext $file</tt> and put the resulting file into <tt class="docutils literal">$file</tt>’s directory with extension <tt class="docutils literal">$ext</tt>. The command will be started in <tt class="docutils literal">$file</tt>’s directory.</dd>
</dl>
</div>
</div>
<div class="section" id="usage">
<h1><a class="toc-backref" href="#toc-entry-4">Usage</a></h1>
<p>The templating language is <a class="reference external" href="http://py3otemplate.readthedocs.io/en/latest/templating.html">extensively documented</a>, the records are exposed in libreoffice as <tt class="docutils literal">objects</tt>, on which you can also call functions.</p>
<div class="section" id="available-functions-and-objects">
<h2><a class="toc-backref" href="#toc-entry-5">Available functions and objects</a></h2>
<dl class="docutils">
<dt>user</dt>
<dd>Browse record of current user</dd>
<dt>lang</dt>
<dd>The user’s company’s language as string (ISO code)</dd>
<dt>b64decode</dt>
<dd><tt class="docutils literal">base64.b64decode</tt></dd>
<dt>format_multiline_value(string)</dt>
<dd>Generate the ODF equivalent of <tt class="docutils literal">&lt;br/&gt;</tt> and <tt class="docutils literal">&amp;nbsp;</tt> for multiline fields (ODF is XML internally, so those would be skipped otherwise)</dd>
<dt>html_sanitize(string)</dt>
<dd>Sanitize HTML string</dd>
<dt>time</dt>
<dd>Python’s <tt class="docutils literal">time</tt> module</dd>
<dt>display_address(partner)</dt>
<dd>Return a formatted string of the partner’s address</dd>
<dt>o_format_lang(value, lang_code=False, digits=None, grouping=True, monetary=False, dp=False, currency_obj=False, no_break_space=True)</dt>
<dd>Return a formatted numeric or monetary value according to the context language and timezone</dd>
<dt>o_format_date(value, lang_code=False, date_format=False)</dt>
<dd>Return a formatted date or time value according to the context language and timezone</dd>
</dl>
</div>
<div class="section" id="sample-report-templates">
<h2><a class="toc-backref" href="#toc-entry-6">Sample report templates</a></h2>
<p>Sample py3o report templates for the main Odoo native reports (invoice, sale order, purchase order, picking, etc.) are available on the Github project <a class="reference external" href="https://github.com/akretion/odoo-py3o-report-templates">odoo-py3o-report-templates</a>.</p>
</div>
</div>
<div class="section" id="known-issues-roadmap">
<h1><a class="toc-backref" href="#toc-entry-7">Known issues / Roadmap</a></h1>
<ul class="simple">
<li>generate barcode ?</li>
<li>add more detailed example in demo file to showcase features</li>
<li>add migration guide aeroo -&gt; py3o</li>
</ul>
</div>
<div class="section" id="bug-tracker">
<h1><a class="toc-backref" href="#toc-entry-8">Bug Tracker</a></h1>
<p>Bugs are tracked on <a class="reference external" href="https://github.com/OCA/reporting-engine/issues">GitHub Issues</a>.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
<a class="reference external" href="https://github.com/OCA/reporting-engine/issues/new?body=module:%20report_py3o%0Aversion:%2016.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**">feedback</a>.</p>
<p>Do not contact contributors directly about support or help with technical issues.</p>
</div>
<div class="section" id="credits">
<h1><a class="toc-backref" href="#toc-entry-9">Credits</a></h1>
<div class="section" id="authors">
<h2><a class="toc-backref" href="#toc-entry-10">Authors</a></h2>
<ul class="simple">
<li>XCG Consulting</li>
<li>ACSONE SA/NV</li>
</ul>
</div>
<div class="section" id="contributors">
<h2><a class="toc-backref" href="#toc-entry-11">Contributors</a></h2>
<ul class="simple">
<li>Florent Aide (<a class="reference external" href="http://odoo.consulting/">XCG Consulting</a>)</li>
<li>Laurent Mignon &lt;<a class="reference external" href="mailto:laurent.mignon&#64;acsone.eu">laurent.mignon&#64;acsone.eu</a>&gt;,</li>
<li>Alexis de Lattre &lt;<a class="reference external" href="mailto:alexis.delattre&#64;akretion.com">alexis.delattre&#64;akretion.com</a>&gt;,</li>
<li>Guewen Baconnier &lt;<a class="reference external" href="mailto:guewen.baconnier&#64;camptocamp.com">guewen.baconnier&#64;camptocamp.com</a>&gt;</li>
<li>Omar Castiñeira &lt;<a class="reference external" href="mailto:omar&#64;comunitea.com">omar&#64;comunitea.com</a>&gt;</li>
<li>Holger Brunn &lt;<a class="reference external" href="mailto:hbrunn&#64;therp.nl">hbrunn&#64;therp.nl</a>&gt;</li>
<li>Phuc Tran Thanh &lt;<a class="reference external" href="mailto:phuc&#64;trobz.com">phuc&#64;trobz.com</a>&gt;</li>
</ul>
</div>
<div class="section" id="maintainers">
<h2><a class="toc-backref" href="#toc-entry-12">Maintainers</a></h2>
<p>This module is maintained by the OCA.</p>
<a class="reference external image-reference" href="https://odoo-community.org"><img alt="Odoo Community Association" src="https://odoo-community.org/logo.png" /></a>
<p>OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.</p>
<p>This module is part of the <a class="reference external" href="https://github.com/OCA/reporting-engine/tree/16.0/report_py3o">OCA/reporting-engine</a> project on GitHub.</p>
<p>You are welcome to contribute. To learn how please visit <a class="reference external" href="https://odoo-community.org/page/Contribute">https://odoo-community.org/page/Contribute</a>.</p>
</div>
</div>
</div>
</body>
</html>
