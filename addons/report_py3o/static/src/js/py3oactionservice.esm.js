/** @odoo-module **/

import {registry} from "@web/core/registry";
import { browser } from "@web/core/browser/browser";
import Dialog from 'web.Dialog';


function previewPdf(options){
    return new Promise(function(resolve, reject){
        const xhr = new browser.XMLHttpRequest();
        xhr.responseType = 'blob';
        xhr.onreadystatechange = function() {
            if (xhr.readyState == 4) {
                if(xhr.status == 200){
                    var blob = xhr.response; // Get the blob response
                    resolve(blob);
                } else {
                    reject(xhr.response);
                }
            }
        };
        xhr.open("POST", '/report/download');
        let data = new FormData();
        Object.entries(options).forEach((entry) => {
            const [key, value] = entry;
            data.append(key, value);
        });
        data.append("token", "dummy-because-api-expects-one");
        if (odoo.csrf_token) {
            data.append("csrf_token", odoo.csrf_token);
        }
        xhr.send(data);
    })
}

registry
    .category("ir.actions.report handlers")
    .add("py3o_handler", async function (action, options, env) {
        if (action.report_type === "py3o") {
            let url = `/report/py3o/${action.report_name}`;
            const actionContext = action.context || {};
            if (
                _.isUndefined(action.data) ||
                _.isNull(action.data) ||
                (_.isObject(action.data) && _.isEmpty(action.data))
            ) {
                // Build a query string with `action.data` (it's the place where reports
                // using a wizard to customize the output traditionally put their options)
                if (actionContext.active_ids) {
                    var activeIDsPath = "/" + actionContext.active_ids.join(",");
                    url += activeIDsPath;
                }
            } else {
                var serializedOptionsPath =
                    "?options=" + encodeURIComponent(JSON.stringify(action.data));
                serializedOptionsPath +=
                    "&context=" + encodeURIComponent(JSON.stringify(actionContext));
                url += serializedOptionsPath;
            }
            env.services.ui.block();
            try {
                const blob = await previewPdf({
                    data: JSON.stringify([url, action.report_type]),
                    context: JSON.stringify(env.services.user.context),
                });
                const dialog = new Dialog(this, {
                    title: "Xem trước",
                    size: 'large',
                    $content: $(`<embed/>`,{
                        src: URL.createObjectURL(blob),
                        type: 'application/pdf',
                        style: 'height: 1000px;width: 100%'
                    })
                });
                dialog.open();
            } finally {
                env.services.ui.unblock();
            }
            const onClose = options.onClose;
            if (action.close_on_report_download) {
                return env.services.action.doAction(
                    {type: "ir.actions.act_window_close"},
                    {onClose}
                );
            } else if (onClose) {
                onClose();
            }
            return Promise.resolve(true);
        }
        return Promise.resolve(false);
    });
