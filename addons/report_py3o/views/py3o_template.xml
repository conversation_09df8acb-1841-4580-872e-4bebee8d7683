<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="py3o_template_configuration_search_view" model="ir.ui.view">
        <field name="name">py3o.template.configuration.search.view</field>
        <field name="model">py3o.template</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" />
                <field name="filetype" />
                <group string="Group By" name="groupby">
                    <filter
                        name="filetype_groupby"
                        string="File Type"
                        context="{'group_by': 'filetype'}"
                    />
                </group>
            </search>
        </field>
    </record>
    <record id="py3o_template_configuration_form_view" model="ir.ui.view">
        <field name="name">py3o.template.configuration.form.view</field>
        <field name="model">py3o.template</field>
        <field name="arch" type="xml">
            <form>
                <group name="main">
                    <field name="name" />
                    <field name="filetype" />
                    <field name="py3o_template_data" filename="name" />
                </group>
            </form>
        </field>
    </record>
    <record id="py3o_template_configuration_tree_view" model="ir.ui.view">
        <field name="name">py3o.template.configuration.tree.view</field>
        <field name="model">py3o.template</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name" />
                <field name="filetype" />
            </tree>
        </field>
    </record>
    <record id="py3o_template_configuration_action" model="ir.actions.act_window">
        <field name="name">Py3o Templates</field>
        <field name="res_model">py3o.template</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem
        id="py3o_template_configuration_menu"
        parent="base.reporting_menuitem"
        action="py3o_template_configuration_action"
        sequence="100"
    />
</odoo>
