# -*- coding:utf-8 -*-

{
    'name': "Welly Base",
    'summary': "",
    'description': """
       Welly Fitness Base
                    """,
    'version': "********",
    'category': 'Welly',
    'author': "",
    'website': "",
    'depends': ['wfms', 'crm', 'sale', 'sale_loyalty', 'account', 'report_py3o', 'sale_management', 'calendar', 'account_edi', 'sale_crm', 'welly_printer', 'hr', 'welly_widget'],

    'data': [
        'data/ir_sequence_data.xml',
        'data/cron.xml',
        'data/data.xml',
        'data/calendar_data.xml',
        'data/product_product.xml',
        'data/register_form.xml',
        'data/system_parameter.xml',
        'data/mail_activity_data_new.xml',
        'security/welly_group.xml',
        'security/ir.model.access.csv',
        'security/model_access.xml',
        'security/welly_rule.xml',
        'wizard/account_move_request_cancel_action_views.xml',
        'wizard/crm_opportunity_to_quotation__inherit_views.xml',
        'wizard/sale_order_reject_action_views.xml',
        'wizard/account_move_reject_action_views.xml',
        'wizard/sale_order_signature_views.xml',
        'wizard/account_payment_register_inherit.xml',
        'wizard/create_new_contract_pt_views.xml',
        'wizard/action_change_contract_date_start_end.xml',
        'wizard/edit_identification_card_views.xml',
        'wizard/action_close_contract.xml',
        'wizard/action_change_coach_contract.xml',
        'wizard/action_reject_contract.xml',
        'wizard/action_change_session_number.xml',
        'views/sale_order_inherit_views.xml',        'views/crm_views.xml',
        'views/crm_stage_views.xml',
        'views/crm_stage_group_views.xml',
        'views/crm_conversion_report_views.xml',
        'views/product_category.xml',
        'views/account_move_views.xml',
        'views/account_payment_views.xml',
        'views/sale_order_template_inherit.xml',
        'views/welly_contract.xml',
        'views/res_partner_views.xml',
        'views/meta_data_views.xml',
        'views/res_users_view_form_profile_views.xml',
        # 'views/calendar_event.xml',
        'views/utm_source_views.xml',
        'views/product_template_inherit.xml',
        'views/partner_account_move_views.xml',
        'views/view_account_journal_form_inherit.xml',
        'reports/report_account_move.xml',
        'reports/report_welly_payment_line.xml',
        'reports/report_member_contract.xml',
        'reports/report_coach_contract.xml',
        'reports/res_company_report_setting.xml',
        'wizard/crm_lead_duplicate_views.xml',
        'wizard/crm_change_user_id_views.xml',
        'wizard/crm_change_stage_views.xml',
        'views/res_company_view.xml',
        'views/res_user_views.xml',
        'views/hr_department.xml',
        'views/account_payment_fee.xml',
        'wizard/account_payment_register_fee_inherit.xml',
        'wizard/crm_lead_merge_views.xml',
        'views/menu_items.xml',
        'data/ir_ui_menu_group.xml',
        'data/res_groups.xml',
    ],
    'assets': {
        'mail.assets_messaging': [
            'welly_base/static/src/models/*.js',
        ],
        "web.assets_backend": [
            "welly_base/static/src/js/*.js",
            "welly_base/static/src/css/style.scss",
            "welly_base/static/src/xml/account_payment_extension.xml",
            'welly_base/static/src/widget/identification_card_widget.js',
            'welly_base/static/src/widget/identification_card_widget.xml',
        ]
    },

    'installable': True,
    'auto_install': False,
    'application': True,
}
