from odoo import http
from odoo.http import request
from odoo.service import db
from odoo.api import Environment
import logging

_logger = logging.getLogger(__name__)


class UpdateAppsController(http.Controller):

    # # URL để cập nhật tất cả các module contains 'welly' đã được cài đặt với api-key
    # @http.route('/api/update_welly_apps', auth='none', methods=['GET'])
    # def api_update_welly_apps(self, **kwargs):
    #     system_api_key = request.httprequest.headers.get('api-key')
    #     config_system_api_key = request.env['ir.config_parameter'].sudo().get_param('system_api_key')
    #     if system_api_key != config_system_api_key:
    #         return "Invalid API Key"
    #     # Kết nối vào mỗi database
    #     with request.registry.cursor() as cr:
    #         env = Environment(cr, request.uid, request.context)
    #
    #         # Tìm tất cả các module đã cài đặt
    #         module_obj = env['ir.module.module']
    #         installed_modules = module_obj.sudo().search([('state', '=', 'installed'), ('name', 'ilike', 'welly')])
    #
    #         # Gọi hàm upgrade để cập nhật các module
    #         installed_modules.sudo().button_immediate_upgrade()
    #
    #     return "Modules updated successfully in all databases"

    # # URL để cập nhật tất cả các module contains 'welly' đã được cài đặt khi đã đăng nhập
    # @http.route('/update_welly_apps', auth='user', methods=['GET'])
    # def update_welly_apps(self, **kwargs):
    #     # Kiểm tra quyền của user có phải là admin hay không
    #     if not request.env.user.has_group('base.group_system'):
    #         return "You don't have permission to access this API"
    #     # Kết nối vào mỗi database
    #     with request.registry.cursor() as cr:
    #         env = Environment(cr, request.uid, request.context)
    #
    #         # Tìm tất cả các module đã cài đặt
    #         module_obj = env['ir.module.module']
    #         installed_modules = module_obj.sudo().search([('state', '=', 'installed'), ('name', 'ilike', 'welly')])
    #
    #         # Gọi hàm upgrade để cập nhật các module
    #         installed_modules.sudo().button_immediate_upgrade()
    #
    #     return "Modules updated successfully in all databases"

    # update trường state = approved của biên lai sang trường mới
    @http.route('/update_account_move', type='http', auth='public', methods=['GET'], csrf=False)
    def update_account_move(self, **kwargs):
        account_moves = request.env['account.move'].sudo().search(domain=[('id', '!=', 0)])

        for rec in account_moves:
            if not rec.approval_state:
                try:
                    rec.write({'approval_state': rec.state})
                    if rec.state == 'approved':
                        rec.write({'state': 'posted'})
                except Exception as e:
                    _logger.error(f"Error updating account move {rec.id}: {e}")
        return "OK"

    @http.route('/update_exercise_form_id_contract', type='http', auth='public', methods=['GET'], csrf=False)
    def update_exercise_form_id_contract(self, **kwargs):
        contracts = request.env['welly.contract'].sudo().search(
            domain=[('state', 'not in', ['draft', 'reject', 'cancel']),
                    ('service_type', '=', 'pt'),
                    ('exercise_form_id', '=', False)])

        for rec in contracts:
            try:
                move = rec.welly_invoice_id
                for line in move.sale_order_template_id.sale_order_template_line_ids:
                    move.exercise_form_id = line.product_id.exercise_form_id
                    rec.exercise_form_id = line.product_id.exercise_form_id
                    move.welly_service_ids = line.product_id.welly_service_ids
                    break
            except Exception as e:
                _logger.error(f"Error updating account move {rec.id}: {e}")
        return "OK"

    # # URL để cập nhật tất cả các module đã được cài đặt
    # @http.route('/update_all_apps', auth='user', methods=['GET'])
    # def update_all_apps(self, **kwargs):
    #     # Kiểm tra quyền của user có phải là admin hay không
    #     if not request.env.user.has_group('base.group_system'):
    #         return "You don't have permission to access this API"
    #     # Kết nối vào mỗi database
    #     with request.registry.cursor() as cr:
    #         env = Environment(cr, request.uid, request.context)
    #
    #         # Tìm tất cả các module đã cài đặt
    #         module_obj = env['ir.module.module']
    #         installed_modules = module_obj.sudo().search([('state', '=', 'installed')])
    #
    #         # Gọi hàm upgrade để cập nhật các module
    #         installed_modules.sudo().button_immediate_upgrade()
    #
    #     return "Modules updated successfully in all databases"
