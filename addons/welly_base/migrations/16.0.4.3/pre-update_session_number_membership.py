import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)


def migrate(cr, version):
    cr.execute("""
        update account_move 
        set session_number = 0 
        where session_number > 0 and service_type = 'member'
    """)
    cr.execute("""
        update welly_contract
        set available_session_number = 0
        where available_session_number > 0
        and service_type = 'member'
    """)
    _logger.info("update_session_number_membership")
