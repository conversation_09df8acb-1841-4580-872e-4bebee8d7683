<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_move_form" model="ir.ui.view">
            <field name="name">account.move.form</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <form string="Account Entry" js_class="account_move_form"
                    style="flex-wrap: wrap !important">
                    <script src="/welly_base/static/src/js/fullViewPDF.js"></script>
                    <header>
                        <!-- Post -->
                        <button name="action_post" string="Post" class="oe_highlight"
                            type="object" groups="account.group_account_invoice" data-hotkey="v"
                            context="{'validate_analytic': True}"
                            attrs="{'invisible': ['|', ('hide_post_button', '=', True), ('move_type', '!=', 'entry')]}" />
                        <button name="action_invoice_sent"
                            type="object"
                            string="Send &amp; Print"
                            invisible="1"
                            class="oe_highlight"
                            data-hotkey="y" />
                        <!-- Register Payment (only invoices / receipts) -->
                        <button name="action_register_payment" id="account_invoice_payment_btn"
                            type="object" class="oe_highlight"
                            attrs="{'invisible': ['|', '|', ('payment_state', 'not in', ('not_paid', 'partial')), ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')), ('approval_state', 'not in', ('posted', 'request_cancel'))]}"
                            context="{'dont_redirect_to_payments': True}"
                            string="Register Payment" data-hotkey="g"
                            groups="account.group_account_invoice" />
                        <!-- confirm -->
                        <button name="button_confirm" type="object" string="Submit"
                            class="btn-primary"
                            data-hotkey="o"
                            groups="welly_base.group_welly_base_user"
                            title="Receptionist submit"
                            attrs="{'invisible': [('approval_state', '!=', 'draft')]}" />
                        <!-- Sign -->
                        <button name="button_sign" type="object" string="Approve"
                            class="btn-primary"
                            data-hotkey="o"
                            groups="welly_base.group_admin_club,welly_base.group_coo,welly_base.group_welly_account"
                            title="Account user approve"
                            attrs="{'invisible': ['|', '|',('state', '!=', 'posted'),('payment_state', '!=', 'paid'), ('approval_state', 'not in', ('posted', 'request_cancel'))]}" />
                        <!-- Create new contract -->
                        <field name="payment_state" invisible="1" />
                        <button name="create_new_contract" type="object" string="Create Contract"
                            class="btn-primary"
                            data-hotkey="o"
                            groups="welly_base.group_sales,welly_base.group_coo,welly_base.group_admin_club,welly_base.group_receptionist,welly_base.group_pilates,welly_base.group_pt"
                            title="Create Contract"
                            attrs="{'invisible': ['|', '&amp;', ('payment_type_welly', '=', 'installment'), ('payment_state', 'not in', ['paid', 'partial']), '&amp;', ('payment_type_welly', '=', 'deposit'), ('approval_state', '!=', 'approved')]}"
                            context="{'account_move_id': id}" />

                        <!-- Preview (only customer invoices) -->
                        <button name="preview_invoice" type="object" string="Preview"
                            data-hotkey="o"
                            title="Preview invoice"
                            invisible="1"
                        />
                        <!-- open total receipt -->
                        <button name="open_welly_recept_total_report" type="object" string="Print"
                            title="Print Total Receipt"
                            class="btn-primary"
                            attrs="{'invisible': [('approval_state', 'not in', ('approved','posted', 'request_cancel'))]}"
                        />
                        <!-- button Yêu cầu hủy -->
                        <button name="%(welly_base.action_account_move_request_cancel_form)d"
                            type="action"
                            class="btn-primary"
                            string="Yêu cầu hủy"
                            attrs="{'invisible': [('approval_state', 'not in', ('draft', 'posted'))]}" />

                        <button name="action_account_move_reject" type="object"
                            string="Reject"
                            groups="welly_base.group_admin_club,welly_base.group_coo,welly_base.group_welly_account"
                            attrs="{'invisible': [('approval_state', 'not in', ('posted', 'request_cancel'))]}" />

                        <button name="action_account_move_reject" type="object"
                            string="Reject"
                            groups="welly_base.group_welly_account"
                            attrs="{'invisible': [('approval_state', 'in', ('posted', 'cancel', 'request_cancel'))]}" />

                        <button name="button_draft" string="Reset to Draft" type="object"
                            groups="account.group_account_invoice"
                            attrs="{'invisible' : ['|',('show_reset_to_draft_button', '=', False),('state', 'in', ('posted','cancel'))]}"
                            data-hotkey="q" />
                        <field name="approval_state" widget="statusbar"
                            statusbar_visible="draft,posted,request_cancel,approved" />
                        <field name="state" invisible="1" />
                    </header>

                    <div class="alert alert-warning mb-0" role="alert"
                        attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('duplicated_ref_ids', '=', [])]}">
                        Warning: this bill might be a duplicate of <button
                            name="open_duplicated_ref_bill_view"
                            type="object"
                            string="one of those bills"
                            class="btn btn-link p-0"
                        />
                    </div>
                    <!-- Invoice outstanding credits -->
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                        class="alert alert-warning mb-0" role="alert"
                        attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('tax_lock_date_message', '=', False)]}">
                        <field name="tax_lock_date_message" nolabel="1" />
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                        class="alert alert-info mb-0" role="alert"
                        attrs="{'invisible': ['|', '|', ('move_type', '!=', 'out_invoice'), ('invoice_has_outstanding', '=', False), ('payment_state', 'not in', ('not_paid', 'partial'))]}">
                        You have <bold>
                            <a class="alert-link" href="#outstanding" role="button">outstanding
                        credits
                            </a>
                        </bold>
                        for this customer. You can allocate them to mark this invoice as paid. </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                        class="alert alert-info mb-0" role="alert"
                        attrs="{'invisible': ['|', '|', ('move_type', '!=', 'in_invoice'), ('invoice_has_outstanding', '=', False), ('payment_state', 'not in', ('not_paid', 'partial'))]}">
                        You have <bold>
                            <a class="alert-link" href="#outstanding" role="button">outstanding
                        debits
                            </a>
                        </bold>
                        for this vendor. You can allocate them to mark this bill as paid. </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                        class="alert alert-info mb-0" role="alert"
                        attrs="{'invisible': ['|', '|', ('move_type', '!=', 'out_refund'), ('invoice_has_outstanding', '=', False), ('payment_state', 'not in', ('not_paid', 'partial'))]}">
                        You have <bold>
                            <a class="alert-link" href="#outstanding" role="button">outstanding
                        debits
                            </a>
                        </bold>
                        for this customer. You can allocate them to mark this credit note as paid. </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                        class="alert alert-info mb-0" role="alert"
                        attrs="{'invisible': ['|', '|', ('move_type', '!=', 'in_refund'), ('invoice_has_outstanding', '=', False), ('payment_state', 'not in', ('not_paid', 'partial'))]}">
                        You have <bold>
                            <a class="alert-link" href="#outstanding" role="button">outstanding
                        credits
                            </a>
                        </bold>
                        for this vendor. You can allocate them to mark this credit note as paid. </div>
                    <div class="alert alert-info mb-0" role="alert"
                        attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('auto_post', '!=', 'at_date')]}">
                        This move is configured to be posted automatically at the accounting date:<field
                            name="date" readonly="1" />. </div>
                    <div class="alert alert-info mb-0" role="alert"
                        attrs="{'invisible': ['|', '|', ('state', '!=', 'draft'), ('auto_post', '=', 'no'), ('auto_post', '=', 'at_date')]}">
                        <field name="auto_post" readonly="1" /> auto-posting enabled. Next
                        accounting date:<field name="date" readonly="1" />. <span
                            attrs="{'invisible': [('auto_post_until', '=', False)]}">The recurrence
                        will end on <field name="auto_post_until" readonly="1" /> (included). </span>
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                        class="alert alert-warning mb-0" role="alert"
                        attrs="{'invisible': [('partner_credit_warning', '=', '')]}">
                        <field name="partner_credit_warning" />
                    </div>
                    <!-- Currency consistency -->
                    <div class="alert alert-warning mb-0" role="alert"
                        attrs="{'invisible': ['|', ('display_inactive_currency_warning', '=', False), ('move_type', 'not in', ('in_invoice', 'in_refund', 'in_receipt'))]}">
                        In order to validate this bill, you must <button class="oe_link"
                            type="object" name="action_activate_currency"
                            style="padding: 0; vertical-align: baseline;">
                            activate the currency of
                            the bill</button>. The journal entries need to be computed by Odoo
                        before being posted in your company's currency. </div>
                    <div class="alert alert-warning mb-0" role="alert"
                        attrs="{'invisible': ['|', ('display_inactive_currency_warning', '=', False), ('move_type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}">
                        In order to validate this invoice, you must <button class="oe_link"
                            type="object"
                            name="action_activate_currency"
                            style="padding: 0; vertical-align: baseline;">
                            activate the currency of
                            the invoice</button>. The journal entries need to be computed by Odoo
                        before being posted in your company's currency. </div>
                    <sheet>
                        <div name="button_box" class="oe_button_box">
                            <button name="open_created_caba_entries"
                                class="oe_stat_button"
                                icon="fa-usd"
                                type="object"
                                attrs="{'invisible': [('tax_cash_basis_created_move_ids', '=', [])]}"
                                string="Cash Basis Entries">
                            </button>
                        </div>

                        <!-- Payment status for invoices / receipts -->
                        <widget
                            name="web_ribbon" title="Paid"
                            attrs="{'invisible': ['|', ('payment_state', '!=', 'paid'), ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}" />
                        <widget
                            name="web_ribbon" title="In Payment"
                            attrs="{'invisible': ['|', ('payment_state', '!=', 'in_payment'), ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}" />
                        <widget
                            name="web_ribbon" title="Partial"
                            attrs="{'invisible': ['|', ('payment_state', '!=', 'partial'), ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}" />
                        <widget
                            name="web_ribbon" title="Reversed"
                            bg_color="bg-danger"
                            attrs="{'invisible': [('payment_state', '!=', 'reversed')]}" />
                        <widget
                            name="web_ribbon" text="Invoicing App Legacy"
                            bg_color="bg-info"
                            attrs="{'invisible': [('payment_state', '!=', 'invoicing_legacy')]}"
                            tooltip="This entry has been generated through the Invoicing app, before installing Accounting. It has been disabled by the 'Invoicing Switch Threshold Date' setting so that it does not impact your accounting." />

                        <!-- Invisible fields -->
                        <field name="user_company_ids" invisible="1" />
                        <field name="pt_team_id_related" invisible="1" />
                        <field name="id" invisible="1" />
                        <field name="company_id" invisible="1" />
                        <field name="journal_id" invisible="1" />
                        <field name="show_name_warning" invisible="1" />
                        <field name="posted_before" invisible="1" />
                        <field name="move_type" invisible="1" />
                        <field name="payment_state" invisible="1" force_save="1" />
                        <field name="invoice_filter_type_domain" invisible="1" />
                        <field name="suitable_journal_ids" invisible="1" />
                        <field name="currency_id" invisible="1" />
                        <field name="company_currency_id" invisible="1" />
                        <field name="commercial_partner_id" invisible="1" />
                        <field name="bank_partner_id" invisible="1" />
                        <field name="display_qr_code" invisible="1" />
                        <field name="show_reset_to_draft_button" invisible="1" />

                        <field name="invoice_has_outstanding" invisible="1" />
                        <field name="is_move_sent" invisible="1" />
                        <field name="has_reconciled_entries" invisible="1" />
                        <field name="restrict_mode_hash_table" invisible="1" />
                        <field name="country_code" invisible="1" />
                        <field name="display_inactive_currency_warning" invisible="1" />
                        <field name="statement_line_id" invisible="1" />
                        <field name="payment_id" invisible="1" />
                        <field name="tax_country_id" invisible="1" />
                        <field name="tax_cash_basis_created_move_ids" invisible="1" />
                        <field name="quick_edit_mode" invisible="1" />
                        <field name="hide_post_button" invisible="1" />
                        <field name="duplicated_ref_ids" invisible="1" />
                        <field name="quick_encoding_vals" invisible="1" />

                        <div class="oe_title" style="max-width: calc(100% - 64px) !important;">
                            <h1 class="text-center">
                                <field name="move_type"
                                    attrs="{'invisible': [('move_type', '=', 'entry')]}"
                                    readonly="1" nolabel="1" />
                            </h1>
                            <hr class="mt-0 mb-2" />

                            <div class="text-warning"
                                attrs="{'invisible': [('show_name_warning', '=', False)]}"> The
                                current highest number is <field class="oe_inline"
                                    name="highest_name" /> . You might want to put a higher number
                                here. </div>
                            <h1>
                                <field name="name"
                                    attrs="{
                                        'invisible':[('name', '=', '/'), ('posted_before', '=', False), ('quick_edit_mode', '=', False)]
                                    }"
                                    readonly="1"
                                    placeholder="Draft" />
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="service_type" widget="radio"
                                    options="{'horizontal': true}" nolabel="1" />
                            </group>

                            <group class="welly_border">
                                <field name="welly_location_many2_many" string="Địa Điểm Phòng Tập"
                                    widget="many2many_tags"
                                    options="{'no_create': True, 'no_create_edit':True}"
                                    attrs="{'readonly': [('approval_state', '!=', 'draft')]}" />

                                <field name="represent_id"
                                    options="{'no_create': True, 'no_create_edit':True}"
                                    attrs="{'readonly': [('approval_state', '!=', 'draft')]}"
                                />
                            </group>

                            <div class="welly_section_header" colspan="4">I/ THÔNG TIN CHUNG</div>

                            <!-- Dòng 1: Người sử dụng dịch vụ & CCCD -->
                            <group name="line_1_1">
                                <field name="partner_id" string="Người sử dụng dịch vụ"
                                    help="Người đứng tên hợp đồng"
                                    options="{'no_create': True, 'no_create_edit':True}"
                                    required="True"
                                    attrs="{'invisible': [('approval_state', '!=', 'draft')]}"
                                />
                                <field name="partner_id_number" />
                                <label for="partner_name_print"
                                    attrs="{'invisible': [('approval_state', '=', 'draft')]}">
                                    Người sử dụng dịch vụ
                                </label>
                                <div class="o_row pt-1"
                                    attrs="{'invisible': [('approval_state', '=', 'draft')]}">
                                    <field name="partner_name_print" string="Người sử dụng dịch vụ"
                                        help="Người đứng tên hợp đồng"
                                        readonly="1"
                                    />
                                    <button name="action_view_partner"
                                        type="object"
                                        class="btn btn-link oe_inline"
                                        attrs="{'invisible': [('partner_id', '=', False)]}"
                                        help="Xem thông tin chi tiết của khách hàng"
                                        icon="fa-arrow-right fa-lg"
                                    />
                                </div>
                                <field name="address"
                                    attrs="{'required': [('state', '=', 'draft')]}" />
                                <field name="phone" attrs="{'required': [('state', '=', 'draft')]}" />
                            </group>
                            <!-- Dòng 2: Giới tính & Quốc tịch -->
                            <group name="line_1_2">
                                <field name="gender" attrs="{'required': [('state', '=', 'draft')]}" />
                                <field name="nationality_id" />
                                <field name="birthdate"
                                    attrs="{'required': [('state', '=', 'draft')]}" />
                                <field name="email" />
                            </group>

                            <div class="welly_section_header" colspan="4">II/ THÔNG TIN SẢN PHẨM</div>

                            <!-- Dòng 1: Gói dịch vụ/Thẻ thành viên -->
                            <group name="line_2_1">
                                <!-- Thông tin hình thức tập -->
                                <field name="registration_form_id"
                                    attrs="{'invisible': [('approval_state', '!=', 'draft')]}" />
                                <field name="registration_form_name_print"
                                    string="Hình thức đăng ký" readonly="1"
                                    attrs="{'invisible': [('approval_state', '=', 'draft')]}" />
                                <field name="source_id" string="Nguồn"
                                    attrs="{'readonly': [('approval_state', '=', 'cancel')]}" />

                                <!-- Chuyển nhượng -->
                                <field name="old_contract_id"
                                    attrs="{'readonly':[('approval_state', '!=', 'draft')], 'invisible':['|', ('registration_form_id','==', %(welly_base.welly_registration_form_new)d), ('registration_form_id', '=', False)]}" />
                                <field name="new_contract_id"
                                    attrs="{'readonly':[('approval_state', '!=', 'draft')], 'invisible':['|', ('registration_form_id','!=', %(welly_base.welly_registration_form_transfer)d), ('registration_form_id', '=', False)]}" />

                                <!-- Thông tin PT (chỉ hiện với service_type = 'pt') -->
                                <field name="exercise_form_id"
                                    options="{'no_create': True, 'no_create_edit':True}"
                                    attrs="{'invisible': ['|', ('approval_state', '!=', 'draft'), ('service_type', '!=', 'pt')]}" />
                                <field name="coach_id" string="Huấn luyện viên"
                                    attrs="{'readonly': [('approval_state', '!=', 'draft')], 'invisible': [('service_type', '!=', 'pt')]}" />
                                <field name="exercise_form_name_print" string="Hình thức tập luyện"
                                    readonly="1"
                                    attrs="{'invisible': ['|', ('approval_state', '=', 'draft'), ('service_type', '!=', 'pt')]}" />

                                <!-- Thời hạn -->
                                <label for="valid_time" class="oe_inline"
                                    attrs="{'invisible': [('payment_state', '=', 'reversed')]}">Thời
                                    hạn</label>
                                <div attrs="{'readonly': [('approval_state', '!=', 'draft')]}">
                                    <field name="valid_time"
                                        attrs="{'readonly': [('approval_state', '!=', 'draft')]}"
                                        class="oe_inline" />
                                    <field name="valid_time_type"
                                        attrs="{'readonly': [('approval_state', '!=', 'draft')]}"
                                        class="oe_inline" />
                                </div>

                                <!-- Số buổi/lượt tập -->
                                <field name="session_number" string="Số buổi tập"
                                    attrs="{'readonly': [('approval_state', '!=', 'draft')],'invisible': [('service_type', '!=', 'pt')]}" />
                                <field name="session_number" string="Số lượt"
                                    attrs="{'invisible': [('service_type', 'in', ['member','pt'])]}" />

                                <!-- Ngày hiệu lực -->
                                <field name="date_start" string="Ngày bắt đầu"
                                    attrs="{'readonly': [('approval_state', '!=', 'draft')]}" />
                                <field name="date_end" string="Ngày kết thúc"
                                    attrs="{'readonly': [('approval_state', '!=', 'draft')]}" />

                                <!-- Quà tặng -->
                                <field name="welly_gift_id" string="Quà tặng"
                                    widget="many2many_tags"
                                    options="{'no_create': True, 'no_create_edit':True}"
                                    attrs="{'invisible': [('approval_state', '!=', 'draft')]}" />
                                <field name="welly_gift_name_print" string="Danh Sách Quà Tặng"
                                    readonly="1"
                                    attrs="{'invisible': [('approval_state', '=', 'draft')]}" />
                                <field name="utm_campaign_id" string="Chương trình"
                                    options="{'no_create': True, 'no_create_edit':True}"
                                    attrs="{'invisible': [('approval_state', '!=', 'draft')]}" />
                                <field name="utm_campaign_name_print" string="Chương trình"
                                    readonly="1"
                                    attrs="{'invisible': [('approval_state', '=', 'draft')]}" />
                                <field name="presenter_partner" string="Người giới thiệu"
                                    widget="many2one"
                                    attrs="{'readonly': [('approval_state', '=', 'cancel')]}" />
                            </group>
                            <!-- Dòng 2: Hình thức đăng ký & Dịch vụ -->
                            <group name="line_2_2">
                                <!-- Thẻ thành viên -->
                                <field name="sale_order_template_id" string="Thẻ thành viên"
                                    options='{"no_open": True}'
                                    attrs="{'invisible': ['|', ('approval_state', '!=', 'draft'), ('service_type', '!=', 'member')]}" />
                                <field name="sale_order_template_id" string="Gói dịch vụ"
                                    options='{"no_open": True}'
                                    attrs="{'invisible': ['|', ('approval_state', '!=', 'draft'), ('service_type', 'not in', ['pt','turn_card'])]}" />
                                <field name="sale_order_template_name_print" string="Thẻ thành viên"
                                    readonly="1"
                                    attrs="{'invisible': ['|', ('approval_state', '=', 'draft'), ('service_type', '!=', 'member')]}" />
                                <field name="sale_order_template_name_print" string="Gói dịch vụ"
                                    readonly="1"
                                    attrs="{'invisible': ['|', ('approval_state', '=', 'draft'), ('service_type', 'not in', ['pt','turn_card'])]}" />
                                <field name="welly_service_ids" string="Dịch vụ"
                                    widget="many2many_tags" readonly="1" />
                                <!-- Dịch vụ gia đình (chỉ hiện với member) -->
                                <field name="is_family_service" string="Dịch vụ gia đình"
                                    attrs="{'invisible': [('service_type', 'in', ['pt','turn_card'])]}" />
                                <field name="family_member_qty" string="Số thành viên gia đình"
                                    attrs="{'invisible': [('service_type', 'in', ['pt','turn_card'])]}" />
                            </group>

                            <div class="welly_section_header" colspan="4">III/ THANH TOÁN</div>

                            <!-- Dòng 1: Hình thức thanh toán -->
                            <group name="line_3_1">
                                <field name="payment_type_welly"
                                    attrs="{'readonly': [('approval_state', '!=', 'draft')]}" />
                                <field name="payment_method"
                                    attrs="{'readonly': [('approval_state', '!=', 'draft')]}"
                                    invisible="1" />
                            </group>

                            <!-- Dòng 2: Giá gốc & Giảm giá (%) -->
                            <group name="line_3_2">
                                <field name="total_original_price" string="Giá gốc" readonly="1" />
                                <field name="total_discount" string="Giảm giá (%)"
                                    widget="percentage" readonly="1" />
                                <field name="total_original_price_text" />
                                <field name="total_discount_amount" string="Giảm giá" readonly="1" />
                                <field name="total_discount_amount_text" />
                                <field name="service_price"
                                    attrs="{'invisible':['|', ('registration_form_id','==', %(welly_base.welly_registration_form_new)d), ('registration_form_id', '=', False)]}" />
                                <field name="amount_untaxed"/>
                                <field name="amount_tax"/>
                                <field name="pay_amount" string="Tổng số tiền bằng số" readonly="1" />
                                <field name="service_fee"
                                    attrs="{'readonly':[('approval_state', '!=', 'draft')], 'invisible':['|', ('registration_form_id', '==', %(welly_base.welly_registration_form_new)d), ('registration_form_id', '=', False)]}" />
                                <field name="pay_amount_to_text" />
                            </group>
                        </group>

                        <notebook>
                            <page id="service_users_id"
                                name="service_users"
                                string="Người sử dụng dịch vụ">
                                <field name="partner_account_move_ids"
                                    widget="one2many_list"
                                    options="{'always_reload': True}"
                                    attrs="{'readonly':[('approval_state', '!=', 'draft')]}"
                                    context="{'account_move_id': id}">
                                    <tree editable="bottom">
                                        <control>
                                            <create name="add_line_control" string="Thêm khách hàng" />
                                        </control>
                                        <field name="partner_id" string="Tên" />
                                        <field name="partner_id_number" string="CCCD/Hộ chiếu" />
                                        <field name="phone" string="Điện thoại" />
                                        <field name="email" string="Email" />
                                        <field name="gender" string="Giới tính" />
                                        <field name="birthdate" string="Ngày sinh" />
                                        <field name="nationality_id" string="Quốc tịch" />
                                        <field name="address" string="Địa chỉ" />
                                        <field name="is_attached" string="Người đi kèm" />
                                    </tree>
                                </field>
                            </page>
                            <page string="CCCD" name="cccd_info">
                                <group>
                                    <field name="partner_id_number" string="Số CCCD/Hộ chiếu" />
                                </group>
                                <group colspan="2">
                                    <group>
                                        <field name="identification_card_front"
                                            widget="identification_card_image"
                                            options="{'preview_image': 'identification_card_front', 'zoom': true}" />
                                    </group>
                                    <group>
                                        <field name="identification_card_back"
                                            widget="identification_card_image"
                                            options="{'preview_image': 'identification_card_back', 'zoom': true}" />
                                    </group>
                                </group>
                            </page>
                            <page
                                id="other_contact"
                                name="other_contact"
                                string="Other Contact">
                                <group class="welly_border">
                                    <group>
                                        <field name="other_contact_id"
                                            attrs="{'readonly': [('approval_state', '!=', 'draft')],'invisible': [('approval_state', '!=', 'draft')]}" />
                                        <field name="other_contact_name_print" string="Liên hệ"
                                            readonly="1"
                                            attrs="{'invisible': [('approval_state', '=', 'draft')]}" />
                                        <field name="other_contact_gender" />
                                        <field name="other_contact_address" />
                                    </group>
                                    <group>
                                        <field name="other_contact_id_number" />
                                        <field name="other_contact_nationality_id" />
                                        <field name="other_contact_phone" />
                                    </group>
                                </group>
                            </page>
                            <page
                                id="guardian"
                                name="guardian"
                                string="Guardian">
                                <group class="welly_border">
                                    <group>
                                        <field name="guardian_id"
                                            attrs="{'readonly': [('approval_state', '!=', 'draft')],'invisible': [('approval_state', '!=', 'draft')]}" />
                                        <field name="guardian_name_print" string="Liên hệ"
                                            readonly="1"
                                            attrs="{'invisible': [('approval_state', '=', 'draft')]}" />
                                        <field name="guardian_gender" />
                                        <field name="guardian_address" />
                                    </group>
                                    <group>
                                        <field name="guardian_id_number" />
                                        <field name="guardian_relationship"
                                            attrs="{'readonly': [('approval_state', '!=', 'draft')]}" />
                                        <field name="guardian_phone" />
                                    </group>
                                </group>
                            </page>
                            <page
                                id="invoice_tab"
                                name="invoice_tab"
                                string="Invoice Lines"
                                attrs="{'invisible': [('move_type', '=', 'entry')]}"
                            >
                                <field name="invoice_line_ids"
                                    widget="section_and_note_one2many"
                                    mode="tree,kanban"
                                    context="{
                                           'default_move_type': context.get('default_move_type'),
                                           'journal_id': journal_id,
                                           'default_partner_id': commercial_partner_id,
                                           'default_currency_id': currency_id or company_currency_id,
                                           'default_display_type': 'product',
                                           'quick_encoding_vals': quick_encoding_vals,
                                       }">
                                    <tree editable="bottom" string="Chi tiết bút toán"
                                        default_order="sequence, id">
                                        <control>
                                            <create name="add_line_control" string="Add a line" />
                                            <create name="add_section_control"
                                                string="Add a section"
                                                context="{'default_display_type': 'line_section'}" />
                                            <create name="add_note_control" string="Add a note"
                                                context="{'default_display_type': 'line_note'}" />
                                        </control>

                                        <!-- Displayed fields -->
                                        <field name="sequence" widget="handle" />
                                        <field name="product_id"
                                            optional="show"
                                            widget="many2one_barcode"
                                            domain="
                                                    context.get('default_move_type') in ('out_invoice', 'out_refund', 'out_receipt')
                                                    and [('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]
                                                    or [('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]
                                               " />
                                        <field name="name" widget="section_and_note_text"
                                            optional="show" />
                                        <field name="account_id"
                                            context="{'partner_id': partner_id, 'move_type': parent.move_type}"
                                            groups="account.group_account_readonly"
                                            options="{'no_create': True}"
                                            domain="[('deprecated', '=', False), ('account_type', 'not in', ('asset_receivable', 'liability_payable')), ('company_id', '=', parent.company_id), ('is_off_balance', '=', False)]"
                                            attrs="{'required': [('display_type', 'not in', ('line_note', 'line_section'))]}" />
                                        <field name="analytic_distribution"
                                            widget="analytic_distribution"
                                            groups="analytic.group_analytic_accounting"
                                            optional="show"
                                            options="{'product_field': 'product_id', 'account_field': 'account_id'}"
                                            business_domain_compute="parent.move_type in ['out_invoice', 'out_refund', 'out_receipt'] and 'invoice' or parent.move_type in ['in_invoice', 'in_refund', 'in_receipt'] and 'bill' or 'general'" />
                                        <field name="quantity" optional="show" />
                                        <field name="product_uom_category_id" invisible="1" />
                                        <field name="product_uom_id" string="UoM"
                                            groups="uom.group_uom" optional="show" />
                                        <field name="price_unit" string="Price" />
                                        <field name="discount" string="Disc.%" optional="hide" />

                                        <field name="tax_ids" widget="many2many_tags"
                                            domain="[('type_tax_use', '=?', parent.invoice_filter_type_domain), ('company_id', '=', parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                            context="{'append_type_to_tax_name': not parent.invoice_filter_type_domain}"
                                            options="{'no_create': True}"
                                            optional="show" />
                                        <field name="price_subtotal"
                                            string="Tổng trước thuế"
                                        />
                                        <field name="price_total"
                                            string="Tổng sau thuế"
                                        />

                                        <!-- Others fields -->
                                        <field name="partner_id" invisible="1" />
                                        <field name="currency_id" invisible="1" />
                                        <field name="company_id" invisible="1" />
                                        <field name="company_currency_id" invisible="1" />
                                        <field name="display_type" force_save="1" invisible="1" />
                                        <!-- /l10n_in_edi.test_edi_json -->
                                        <!-- required for @api.onchange('product_id') -->
                                        <field name="product_uom_id" invisible="1" />
                                    </tree>


                                </field>
                                <group col="12" class="oe_invoice_lines_tab">
                                    <group colspan="8">
                                        <field name="narration" placeholder="Terms and Conditions"
                                            colspan="2" nolabel="1" />
                                    </group>
                                    <!-- Totals (only invoices / receipts) -->
                                    <group colspan="4">
                                        <group class="oe_subtotal_footer oe_right"
                                            attrs="{'invisible': ['|', ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')),
                                                                       ('payment_state' ,'=', 'invoicing_legacy')]}">

                                            <field name="tax_totals"
                                                widget="account-tax-totals-field" nolabel="1"
                                                colspan="2"
                                                attrs="{'readonly': ['|', ('approval_state', '!=', 'draft'), '&amp;', ('move_type', 'not in', ('in_invoice', 'in_refund', 'in_receipt')), ('quick_edit_mode', '=', False)]}" />

                                            <field name="invoice_payments_widget" colspan="2"
                                                nolabel="1" widget="payment" />
                                            <field name="amount_residual"
                                                class="oe_subtotal_footer_separator"
                                                attrs="{'invisible': [('approval_state', '=', 'draft')]}" />
                                        </group>
                                        <field name="invoice_outstanding_credits_debits_widget"
                                            class="oe_invoice_outstanding_credits_debits"
                                            colspan="2" nolabel="1" widget="payment"
                                            attrs="{'invisible': ['|', ('state', 'not in', ('posted','approved')), ('move_type', 'in', ('out_receipt', 'in_receipt'))]}" />
                                    </group>
                                </group>
                            </page>
                            <page string="Payment Lines" name="payment_tab">
                                <field name="account_payment_ids">
                                    <tree editable="false" create="false" delete="false"
                                        edit="false">
                                        <field name="payment_id" />
                                        <field name="date" />
                                        <field name="payment_type_welly" invisible="1" />
                                        <field name="service_type" invisible="1" />
                                        <field name="effective_date"
                                            attrs="{'invisible': [('payment_type_welly', '!=', 'installment')]}" />
                                        <field name="expired_date"
                                            attrs="{'invisible': [('payment_type_welly', '!=', 'installment')]}" />
                                        <field name="allow_session_number"
                                            attrs="{
                                                     'invisible': [
                                                         '|',
                                                         ('service_type', '!=', 'pt'),
                                                         ('payment_type_welly', '!=', 'installment'),
                                                     ],
                                               }"
                                        />
                                        <field name="amount" />
                                        <field name="payment_state" readonly="1" />
                                        <field name="journal_id" readonly="1" />
                                        <field name="currency_id" invisible="1" />
                                        <field name="state" invisible="1" />
                                        <field name="is_reconciled" invisible="1" />
                                        <button name="open_welly_recept_report" type="object"
                                            string="In biên nhận"
                                            class="oe_highlight"
                                            attrs="{'invisible': ['|',('state', '==', 'cancel'),('is_reconciled', '==', False)]}" />
                                    </tree>
                                    <form>
                                        <group>
                                            <field name="payment_type_welly" invisible="1" />
                                            <field name="service_type" invisible="1" />
                                            <field name="payment_id" readonly="1" />
                                            <field name="payer_name" readonly="1" />
                                            <field name="date" readonly="1" />
                                            <field name="amount" readonly="1" />
                                            <field name="allow_session_number"
                                                readonly="1"
                                                attrs="{
                                                       'invisible': [
                                                           '|',
                                                           ('service_type', '!=', 'pt'),
                                                           ('payment_type_welly', '!=', 'installment'),
                                                       ],
                                                  }"
                                            />
                                            <field name="currency_id" invisible="1" />
                                            <field name="payment_state" readonly="1" />
                                        </group>

                                    </form>
                                </field>
                            </page>
                            <page
                                id="aml_tab" string="Chi tiết bút toán"
                                groups="account.group_account_readonly" name="aml_tab">
                                <field name="line_ids"
                                    mode="tree,kanban"
                                    context="{
                                           'default_move_type': context.get('default_move_type'),
                                           'line_ids': line_ids,
                                           'journal_id': journal_id,
                                           'default_partner_id': commercial_partner_id,
                                           'default_currency_id': currency_id or company_currency_id,
                                           'kanban_view_ref': 'account.account_move_line_view_kanban_mobile',
                                       }"
                                    attrs="{'invisible': [('payment_state', '=', 'invoicing_legacy'), ('move_type', '!=', 'entry')]}">
                                    <tree editable="bottom" string="Chi tiết bút toán"
                                        decoration-muted="display_type in ('line_section', 'line_note')"
                                        default_order="sequence, id">
                                        <!-- Displayed fields -->
                                        <field name="account_id"
                                            attrs="{
                                                    'required': [('display_type', 'not in', ('line_section', 'line_note'))],
                                                    'invisible': [('display_type', 'in', ('line_section', 'line_note'))],
                                               }"
                                            domain="[('deprecated', '=', False), ('company_id', '=', parent.company_id)]" />
                                        <!--                                        <field name="partner_id"-->
                                        <!--                                               optional="show"-->
                                        <!--                                               domain="['|', ('parent_id', '=', False), ('is_company',
                                        '=', True)]"-->
                                        <!--                                               attrs="{'column_invisible': [('parent.move_type', '!=',
                                        'entry')]}"/>-->
                                        <field name="name" widget="section_and_note_text"
                                            optional="show" />
                                        <field name="analytic_distribution"
                                            widget="analytic_distribution"
                                            groups="analytic.group_analytic_accounting"
                                            optional="show"
                                            options="{'account_field': 'account_id'}"
                                            business_domain_compute="parent.move_type in ['out_invoice', 'out_refund', 'out_receipt'] and 'invoice' or parent.move_type in ['in_invoice', 'in_refund', 'in_receipt'] and 'bill' or 'general'" />
                                        <field name="date_maturity"
                                            optional="hide"
                                            invisible="context.get('view_no_maturity')"
                                            attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))]}" />
                                        <field name="amount_currency"
                                            groups="base.group_multi_currency"
                                            optional="hide" />
                                        <field name="currency_id" options="{'no_create': True}"
                                            optional="hide" groups="base.group_multi_currency"
                                            attrs="{'column_invisible': [('parent.move_type', '!=', 'entry')]}" />
                                        <field name="tax_ids" widget="autosave_many2many_tags"
                                            optional="hide"
                                            domain="[('type_tax_use', '=?', parent.invoice_filter_type_domain)]"
                                            context="{'append_type_to_tax_name': not parent.invoice_filter_type_domain}"
                                            options="{'no_create': True}"
                                            force_save="1"
                                            attrs="{'readonly': [
                                                    '|', '|',
                                                    ('display_type', 'in', ('line_section', 'line_note')),
                                                    ('tax_line_id', '!=', False),
                                                    '&amp;',
                                                    ('parent.move_type', 'in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')),
                                                    ('account_type', 'in', ('asset_receivable', 'liability_payable')),
                                                ]}" />
                                        <field name="debit"
                                            sum="Total Debit"
                                            attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))], 'readonly': [('parent.move_type', 'in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')), ('display_type', 'in', ('line_section', 'line_note', 'product'))]}" />
                                        <field name="credit"
                                            sum="Total Credit"
                                            attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))], 'readonly': [('parent.move_type', 'in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')), ('display_type', 'in', ('line_section', 'line_note', 'product'))]}" />
                                        <field name="balance" invisible="1" />
                                        <field name="discount_date"
                                            string="Discount Date"
                                            optional="hide"
                                        />
                                        <field name="discount_amount_currency"
                                            string="Discount Amount"
                                            optional="hide"
                                        />

                                        <field name="tax_tag_ids"
                                            widget="many2many_tags"
                                            string="Tax Grids"
                                            optional="show"
                                            options="{'no_create': True}"
                                            domain="[
                                                    ('applicability', '=', 'taxes'),
                                                    '|', ('country_id', '=', parent.tax_country_id),
                                                    ('country_id', '=', False),
                                                ]" />

                                        <field name="tax_tag_invert" readonly="1" optional="hide"
                                            groups="base.group_no_one" />

                                        <!-- Buttons -->
                                        <button name="action_automatic_entry"
                                            type="object"
                                            icon="fa-calendar"
                                            string="Cut-Off"
                                            aria-label="Change Period"
                                            class="float-end"
                                            attrs="{'invisible': [('account_internal_group', 'not in', ('income', 'expense'))], 'column_invisible': ['|', ('parent.move_type', '=', 'entry'), ('parent.state', '!=', 'posted')]}"
                                            context="{'hide_automatic_options': 1, 'default_action': 'change_period'}" />

                                        <!-- Others fields -->
                                        <field name="tax_line_id" invisible="1" />
                                        <field name="company_currency_id" invisible="1" />
                                        <field name="display_type" force_save="1" invisible="1" />
                                        <field name="company_id" invisible="1" />
                                        <field name="sequence" invisible="1" />
                                        <field name="id" invisible="1" />
                                        <field name="account_internal_group" invisible="1" />
                                        <field name="account_type" invisible="1" />
                                    </tree>
                                    <!-- Form view to cover mobile use -->
                                    <form>
                                        <group>
                                            <field name="account_id"
                                                domain="[('company_id', '=', parent.company_id), ('deprecated', '=', False)]" />
                                            <field name="partner_id"
                                                domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]" />
                                            <field name="name" />
                                            <field name="analytic_distribution"
                                                widget="analytic_distribution"
                                                groups="analytic.group_analytic_accounting" />
                                            <field name="amount_currency"
                                                groups="base.group_multi_currency" />
                                            <field name="company_currency_id" invisible="1" />
                                            <field name="company_id" invisible="1" />
                                            <field name="currency_id" options="{'no_create': True}"
                                                groups="base.group_multi_currency" />
                                            <field name="debit" sum="Total Debit" />
                                            <field name="credit" sum="Total Credit" />
                                            <field name="balance" invisible="1" />
                                            <field name="tax_ids" string="Taxes Applied"
                                                widget="autosave_many2many_tags"
                                                options="{'no_create': True}" />
                                            <field name="date_maturity" required="0"
                                                invisible="context.get('view_no_maturity', False)" />
                                        </group>
                                    </form>
                                </field>
                                <div class="alert alert-info text-center mb-0" role="alert"
                                    attrs="{'invisible': ['|', ('payment_state', '!=', 'invoicing_legacy'), ('move_type', '=', 'entry')]}">
                                    <span>This entry has been generated through the Invoicing app,
                                        before installing Accounting. Its balance has been imported
                                        separately.
                                    </span>
                                </div>
                            </page>
                            <page
                                id="other_tab" string="Other Info" name="other_info"
                                attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund'))]}"
                                invisible="1">
                                <group id="other_tab_group">
                                    <group string="Invoice"
                                        name="sale_info_group"
                                        attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund'))]}">
                                        <label for="ref" string="Customer Reference" />
                                        <field name="ref" nolabel="1" />
                                        <field name="user_id" invisible="1" force_save="1" />
                                        <field name="invoice_user_id"
                                            domain="[('share', '=', False)]"
                                            widget="many2one_avatar_user" />
                                        <field name="invoice_origin" string="Source Document"
                                            force_save="1" invisible="1" />
                                        <field name="partner_bank_id"
                                            context="{'default_partner_id': bank_partner_id}"
                                            domain="[('partner_id', '=', bank_partner_id)]"
                                            attrs="{'readonly': [('approval_state', '!=', 'draft')]}" />
                                        <field name="qr_code_method"
                                            attrs="{'invisible': [('display_qr_code', '=', False)]}" />
                                    </group>
                                    <group string="Accounting"
                                        name="accounting_info_group"
                                        attrs="{'invisible': [('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund'))]}">
                                        <field name="company_id" groups="base.group_multi_company" />
                                        <field name="invoice_incoterm_id" />
                                        <field name="fiscal_position_id" />
                                        <field name="invoice_cash_rounding_id"
                                            groups="account.group_cash_rounding" />
                                        <field name="invoice_source_email"
                                            widget="email"
                                            attrs="{'invisible': ['|', ('move_type', 'not in', ('in_invoice', 'in_refund')), ('invoice_source_email', '=', False)]}" />
                                        <field name="auto_post"
                                            attrs="{'readonly': [('state','!=','draft')]}" />
                                        <field name="auto_post_until"
                                            attrs="{'invisible': [('auto_post', 'in', ('no', 'at_date'))],
                                                       'readonly': [('approval_state', '!=', 'draft')]}" />
                                        <field name="to_check" />
                                    </group>
                                </group>
                            </page>
                            <page
                                id="other_tab_entry" string="Other Info" name="other_info"
                                attrs="{'invisible': [('move_type', '!=', 'entry')]}" invisible="1">
                                <group id="other_tab_entry_group">
                                    <group name="misc_group">
                                        <field name="auto_post"
                                            attrs="{'invisible': [('move_type', '!=', 'entry')], 'readonly': [('state','!=','draft')]}" />
                                        <field name="reversed_entry_id"
                                            attrs="{'invisible': [('move_type', '!=', 'entry')]}" />
                                        <field name="auto_post_until"
                                            attrs="{'invisible': [('auto_post', 'in', ('no', 'at_date'))],
                                                       'readonly': [('approval_state', '!=', 'draft')]}" />
                                        <field name="to_check"
                                            attrs="{'invisible': [('move_type', '!=', 'entry')]}" />
                                    </group>
                                    <group>
                                        <field name="fiscal_position_id" />
                                        <field name="company_id" groups="base.group_multi_company"
                                            required="1" />
                                    </group>
                                </group>
                                <!-- Internal note -->
                                <field name="narration" placeholder="Add an internal note..."
                                    nolabel="1" height="50" />
                            </page>
                        </notebook>
                    </sheet>
                    <!-- Attachment preview -->
                    <div class="o_attachment_preview"
                        attrs="{'invisible': ['|',
                                ('move_type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')),
                                ('approval_state', '!=', 'draft')]}" />
                    <!-- Chatter -->
                    <div class="oe_chatter">
                        <field name="message_ids" />
                        <field name="message_follower_ids" groups="base.group_user" />
                        <field name="activity_ids" />
                    </div>
                </form>
            </field>
        </record>
        <record id="account_action_recept_new" model="ir.actions.act_window">
            <field name="name">Recept</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">form</field>
            <field name="view_ids"
                eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'form', 'sequence': 2, 'view_id': ref('welly_base.view_move_form')}),
            ]" />
            <field name="domain">[('opportunity_id', '=', active_id)]</field>
            <field name="context">{'search_default_opportunity_id': active_id,
                'default_opportunity_id': active_id}
            </field>
        </record>
        <record id="welly_account_move_inherit_tree" model="ir.ui.view">
            <field name="name">welly_account_move_inherit_tree</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_out_invoice_tree" />
            <field name="arch" type="xml">
                <xpath expr="/tree/field[@name='invoice_partner_display_name']" position="replace">
                    <field name="service_type" string="Loại dịch vụ" />
                    <field name="source_id" string="Nguồn" optional="show" />
                    <field name="sale_order_template_name_print" string="Gói dịch vụ"
                        optional="show" />
                    <field name="partner_name_print"
                        options="{'no_create': True, 'no_create_edit':True, 'no_open': True}"
                        string="Khách hàng" />
                    <field name="phone" string="Số điện thoại" optional="show" />
                    <field name="payment_type_welly" string="Loại Thanh Toán" optional="show" />
                    <field name="pt_staff_id" widget="many2many_tags" string="PT hỗ trợ"
                        optional="show" />
                    <field name="marketing_staff_id" widget="many2many_tags" string="NV kinh doanh"
                        optional="show" />
                </xpath>
                <xpath expr="(/tree/field[@name='invoice_partner_display_name'])[1]"
                    position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='date']" position="before">
                    <field name="create_date" />
                </xpath>
                <xpath expr="//field[@name='state']" position="replace" />
                <xpath expr="//field[@name='payment_state']" position="after">
                    <field name="approval_state" widget="badge"
                        decoration-success="approval_state in ('approved', 'posted')"
                        decoration-info="approval_state == 'draft'"
                        decoration-danger="approval_state in ('cancel', 'request_cancel')"
                        optional="show" />
                    <field name="state" string="Trạng thái bút toán" widget="badge"
                        decoration-danger="state == 'cancel'" decoration-success="state == 'posted'"
                        decoration-info="state == 'draft'" optional="show" />
                </xpath>
                <xpath expr="//field[@name='activity_ids']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='date']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='ref']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='amount_untaxed_signed']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='amount_total_signed']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='amount_total_in_currency_signed']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <!-- Đặt cột "Số tiền đã trả" sau cột "Tổng" -->
                <xpath expr="//field[@name='amount_total_signed']" position="after">
                    <field name="amount_paid" string="Số tiền đã trả" optional="show"
                        sum="Tồng tiền đã trả" />
                </xpath>
            </field>
        </record>

        <!-- Kế thừa và tùy chỉnh search view của account.move -->
        <record id="account_move_search_view_inherit" model="ir.ui.view">
            <field name="name">account.move.search.inherit</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_account_invoice_filter" />
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="phone" />
                </xpath>
                <xpath expr="//filter[@name='draft']" position="attributes">
                    <attribute name="domain">[('approval_state', '=', 'draft')]</attribute>
                    <attribute name="string">Dự thảo</attribute>
                </xpath>
                <xpath expr="//filter[@name='posted']" position="attributes">
                    <attribute name="domain">[('approval_state', '=', 'posted')]</attribute>
                    <attribute name="string">Đã vào sổ</attribute>
                </xpath>
                <xpath expr="//filter[@name='posted']" position="after">
                    <filter name="approved" string="Đã phê duyệt"
                        domain="[('approval_state', '=', 'approved')]" />
                </xpath>
                <xpath expr="//filter[@name='cancel']" position="attributes">
                    <attribute name="domain">[('approval_state', '=', 'cancel')]</attribute>
                    <attribute name="string">Đã hủy</attribute>
                </xpath>
                <xpath expr="//filter[@name='open']" position="attributes">
                    <attribute name="domain">[('state', '=', 'posted'), ('payment_state', '=',
                        'not_paid')]</attribute>
                    <attribute name="string">Chưa trả</attribute>
                </xpath>
                <xpath expr="//filter[@name='open']" position="after">
                    <filter name="partial" string="Thanh toán một phần"
                        domain="[('state', '=', 'posted'), ('payment_state', '=', 'partial')]" />
                </xpath>
                <xpath expr="//filter[@name='status']" position="before">
                    <filter name="approval_state" string="Trạng thái phê duyệt"
                        context="{'group_by':'approval_state'}" />
                </xpath>
                <xpath expr="//filter[@name='status']" position="attributes">
                    <attribute name="string">Trạng thái bút toán</attribute>
                </xpath>
                <xpath expr="//filter[@name='cancel']" position="after">
                    <filter name="invoice_date_current_month"
                        string="Ngày hóa đơn trong tháng"
                        domain="[('invoice_date', '>=', time.strftime('%Y-%m-01'))]" />
                    <filter string="Ngày hóa đơn tháng trước" name="invoice_date_last_month"
                        domain="[('invoice_date', '=', 'last_month')]" />
                </xpath>
                <xpath expr="//filter[@name='groupy_by_partner']" position="attributes">
                    <attribute name="string">Khách hàng</attribute>
                </xpath>
                <xpath expr="//filter[@name='groupy_by_journal']" position="attributes">
                    <attribute name="string">Sổ kế toán</attribute>
                </xpath>
                <xpath expr="//filter[@name='group_by_date']" position="attributes">
                    <attribute name="string">Ngày hạch toán</attribute>
                </xpath>
                <xpath expr="//filter[@name='group_by_company']" position="attributes">
                    <attribute name="string">Công ty</attribute>
                </xpath>
                <xpath expr="//filter[@name='salesperson']" position="attributes">
                    <attribute name="context">{'group_by': 'marketing_staff_id'}</attribute>
                </xpath>
            </field>
        </record>

        <record id="account_move_search_archive_view_inherit" model="ir.ui.view">
            <field name="name">account.move.search.archive.inherit</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_account_invoice_filter" />
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='myinvoices']" position="after">
                    <separator />
                    <filter string="Archived" name="wellyarchived" domain="[('active', '=', False)]" />
                </xpath>
            </field>
        </record>

        <!--        view graph-->
        <record id="view_account_move_welly_graph" model="ir.ui.view">
            <field name="name">account.move.welly.graph</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <graph string="Biên lai" sample="1">
                    <field name="service_type" />
                    <field name="marketing_staff_id" string="Nhân viên kinh doanh" />
                    <field name="amount_total_signed" string="Tổng tiền thanh toán" type="measure" />
                </graph>
            </field>
        </record>

        <record id="action_move_out_invoice_type_welly" model="ir.actions.act_window">
            <field name="name">Invoices</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,form,graph</field>
            <field name="view_ids"
                eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'sequence': 1, 'view_id': ref('account.view_out_invoice_tree')}),
                (0, 0, {'view_mode': 'form', 'sequence': 2, 'view_id': ref('welly_base.view_move_form')}),
                (0, 0, {'view_mode': 'graph', 'sequence': 3, 'view_id': ref('welly_base.view_account_move_welly_graph')}),
            ]" />
            <field name="search_view_id" ref="welly_base.account_move_search_view_inherit" />
            <field name="domain">[('move_type', '=', 'out_invoice')]</field>
            <field name="context">{'default_move_type': 'out_invoice', 'date_field': 'invoice_date',
                'date_field_type': 'date', 'date_field_label': 'Ngày hóa đơn'}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a customer invoice
                </p>
                <p>
                    Create invoices, register payments and keep track of the discussions with your
                    customers.
                </p>
            </field>
        </record>


        <!--Công
        việc của tôi-->
        <record id="view_account_move_my_activity_tree" model="ir.ui.view">
            <field name="name">account.move.my.activity.tree</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name" />
                    <field name="partner_id" />
                    <field name="phone" />
                    <field name="activity_ids" widget="list_activity" string="Tên công việc" />
                    <field name="activity_type_id" string="Loại công việc" />
                    <field name="my_activity_date_deadline" string="Hạn chót"
                        widget="remaining_days" options="{'allow_order': '1'}" />
                    <field name="activity_user_id" />
                </tree>
            </field>
        </record>

        <record id="account_move_filter_my_activity" model="ir.ui.view">
            <field name="name">account.move.my.activity.filter</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <search>
                    <filter string="Công việc của tôi"
                        name="my_activity"
                        domain="[('activity_user_id', '=', uid)]" />
                </search>
            </field>
        </record>

        <record id="action_report_account_move_my_tasks" model="ir.actions.act_window">
            <field name="name">Công việc của tôi - Biên lai</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,form,activity</field>
            <field name="view_ids"
                eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'sequence': 1, 'view_id': ref('welly_base.view_account_move_my_activity_tree')}),
                (0, 0, {'view_mode': 'form', 'sequence': 2, 'view_id': ref('welly_base.view_move_form')})
            ]" />
            <field name="search_view_id" ref="welly_base.account_move_filter_my_activity" />
            <field name="domain">[('move_type', '=', 'out_invoice'), ('activity_ids','!=',False)]</field>
            <field name="context">
                {
                "search_default_my_activity": 1
                }
            </field>
        </record>
    </data>
</odoo>