# -*- coding: utf-8 -*-
# from odoo import http


# class WellyPayroll(http.Controller):
#     @http.route('/welly_payroll/welly_payroll', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/welly_payroll/welly_payroll/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('welly_payroll.listing', {
#             'root': '/welly_payroll/welly_payroll',
#             'objects': http.request.env['welly_payroll.welly_payroll'].search([]),
#         })

#     @http.route('/welly_payroll/welly_payroll/objects/<model("welly_payroll.welly_payroll"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('welly_payroll.object', {
#             'object': obj
#         })
