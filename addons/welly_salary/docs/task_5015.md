# Task #5015 - [<PERSON><PERSON><PERSON> viên] Cho phép sửa lịch sử lương

## Description
Chức năng: Nhân viên\\ Nhân viên\\ tab **Lương**

Điều kiện tiên quyết:
- <PERSON><PERSON>ời dùng đăng nhập thành công và được phân quyền
- <PERSON><PERSON> tồn tại lịch sử lương

### 1. <PERSON><PERSON><PERSON>n
- <PERSON><PERSON><PERSON> diện chỉnh sửa lương (xem file đính kèm 9055.Chỉnh_sửa_lịch_sử_lương.png)
- <PERSON>iao diện chỉnh sửa lương (xem file đính kèm 9056.image.png)

### 2. <PERSON><PERSON> tả

#### 2.1. <PERSON><PERSON><PERSON> năng Sửa lịch sử lương
- <PERSON><PERSON> quyền: Không phân quyền --> Sử dụng terabit
- <PERSON>hao tác:
  - B1. Ng<PERSON><PERSON>i dùng click button Sửa
  - B2. <PERSON><PERSON> thống bật popup chi tiết lịch sử lương, enable tất cả các trường thông tin (bao g<PERSON><PERSON> phụ cấp trong/ngoài lương)
  - B3. Người dùng chỉnh sửa và Lưu
- Kết quả: Người dùng lưu lịch sử lương, thực hiện check thông tin như sau
  - Nếu người dùng chỉnh sửa trường **Mô tả**: Lưu lịch sử lương thành công
  - Nếu người dùng chỉnh sửa các trường thông tin còn lại thì thông báo "Chỉnh sửa lương sẽ không làm thay đổi các phiếu lương đã xác nhận. Bạn có muốn tiếp tục?"
    - Lưu:
      - Nếu lịch sử lương bị sửa trùng Từ ngày...Đến ngày với lịch sử lương khác: thông báo "Không thể sửa lịch sử lương do trùng thời gian áp dụng"
      - Nếu lịch sử lương bị sửa Đến ngày = Null, và lịch sử lương đó không phải lịch sử lương cuối cùng: thông báo "Chỉ được điều chỉnh Đến ngày = Null cho lịch sử lương cuối cùng"
      - Nếu không vi phạm điều kiện gì:
        - Cập nhật lịch sử lương theo thông tin đã sửa
        - Lưu log sửa lịch sử lương

#### 2.2. Chức năng Xóa lịch sử lương
- Phân quyền: Không phân quyền --> Sử dụng terabit
- Thao tác:
  - B1. Người dùng click button Xóa
  - B2. Hệ thống bật popup thông báo "Xóa lịch sử lương không làm thay đổi các phiếu lương đã xác nhận. Bạn có muốn tiếp tục?"
  - B3. Người dùng xác nhận
    - 3.1. Bấm "Hủy": Đóng popup, cancel thao tác xóa lịch sử lương
    - 3.2. Bấm "Xác nhận":
      - Xóa mềm lịch sử lương, update active trong DB = false --> check lại bảng lương có tính toán với lịch sử lương bị xóa mềm không
      - Lưu log xóa lịch sử lương

## Analysis
Dựa trên yêu cầu, tôi cần:

1. **Phân tích cấu trúc hiện tại:**
   - Model `hr.salary.history` đã có sẵn với các trường cần thiết
   - View tree và form đã có sẵn nhưng chưa có button Edit/Delete
   - Chưa có trường `active` để soft delete
   - Chưa có logging mechanism cho salary history

2. **Cần thêm/sửa:**
   - Thêm trường `active` vào model `hr.salary.history`
   - Thêm button Edit và Delete vào tree view
   - Tạo wizard để edit salary history với validation
   - Tạo confirmation dialog cho delete
   - Implement logging cho các thao tác edit/delete
   - Cập nhật form view để enable/disable các trường

## Task Breakdown Checklist

### 1. Cập nhật Model hr.salary.history
- [x] 1.1: Thêm trường `active` với default=True
- [x] 1.2: Thêm inherit mail.thread để có logging
- [x] 1.3: Thêm method logging cho edit/delete operations
- [x] 1.4: Override write method để log changes
- [x] 1.5: Override unlink method để soft delete

### 2. Tạo Wizard Edit Salary History
- [x] 2.1: Tạo model `hr.salary.history.edit.wizard`
- [x] 2.2: Tạo form view cho wizard
- [x] 2.3: Implement validation logic cho các trường
- [x] 2.4: Tạo confirmation dialog khi edit non-description fields
- [x] 2.5: Implement save logic với logging

### 3. Cập nhật Views
- [x] 3.1: Thêm button Edit và Delete vào tree view của salary history
- [x] 3.2: Cập nhật domain để chỉ hiển thị active records
- [x] 3.3: Thêm action cho Edit button
- [x] 3.4: Thêm action cho Delete button với confirmation

### 4. Implement Delete Functionality
- [x] 4.1: Tạo wizard confirmation cho delete
- [x] 4.2: Implement soft delete logic
- [x] 4.3: Thêm logging cho delete operation
- [x] 4.4: Check impact trên payroll calculations

### 5. Security và Access Rights
- [x] 5.1: Cập nhật ir.model.access.csv cho wizard models
- [x] 5.2: Đảm bảo terabit access như yêu cầu

## Implementation Plan
1. Bắt đầu với việc cập nhật model hr.salary.history
2. Tạo các wizard cần thiết
3. Cập nhật views và thêm buttons
4. Implement logging mechanism
5. Test các chức năng edit và delete

## Dependencies
- Model: hr.salary.history
- Model: hr.employee
- Model: hr.payroll (để check impact)
- Views: hr_salary_history_views.xml
- Security: ir.model.access.csv

## Manual Testcase

### 1. Test Chức năng Sửa lịch sử lương - Chỉ sửa mô tả
- [ ] 1.1: Đăng nhập vào hệ thống với user có quyền
- [ ] 1.2: Vào menu Nhân viên > chọn 1 nhân viên có lịch sử lương
- [ ] 1.3: Chuyển sang tab "Lương"
- [ ] 1.4: Click button "Sửa" trên 1 record lịch sử lương
- [ ] 1.5: Popup "Chỉnh sửa lịch sử lương" hiển thị với các trường đã điền sẵn
- [ ] 1.6: Chỉ thay đổi trường "Mô tả", không thay đổi trường khác
- [ ] 1.7: Click "Lưu"
- [ ] 1.8: Kiểm tra lịch sử lương được cập nhật thành công
- [ ] 1.9: Kiểm tra log activity được tạo trong chatter của nhân viên

### 2. Test Chức năng Sửa lịch sử lương - Sửa các trường quan trọng
- [ ] 2.1: Đăng nhập vào hệ thống với user có quyền
- [ ] 2.2: Vào menu Nhân viên > chọn 1 nhân viên có lịch sử lương
- [ ] 2.3: Chuyển sang tab "Lương"
- [ ] 2.4: Click button "Sửa" trên 1 record lịch sử lương
- [ ] 2.5: Thay đổi các trường: Lương hợp đồng, Lương cơ bản, Lương hiệu quả, Phụ cấp trong/ngoài lương
- [ ] 2.6: Click "Lưu"
- [ ] 2.7: Popup cảnh báo hiển thị: "Chỉnh sửa lương sẽ không làm thay đổi các phiếu lương đã xác nhận. Bạn có muốn tiếp tục?"
- [ ] 2.8: Click "Xác nhận"
- [ ] 2.9: Kiểm tra lịch sử lương được cập nhật thành công
- [ ] 2.10: Kiểm tra log activity chi tiết được tạo trong chatter của nhân viên

### 3. Test Chức năng Xóa lịch sử lương - Hủy thao tác
- [ ] 3.1: Đăng nhập vào hệ thống với user có quyền
- [ ] 3.2: Vào menu Nhân viên > chọn 1 nhân viên có lịch sử lương
- [ ] 3.3: Chuyển sang tab "Lương"
- [ ] 3.4: Click button "Xóa" trên 1 record lịch sử lương
- [ ] 3.5: Popup cảnh báo hiển thị: "Xóa lịch sử lương không làm thay đổi các phiếu lương đã xác nhận. Bạn có muốn tiếp tục?"
- [ ] 3.6: Click "Hủy"
- [ ] 3.7: Popup đóng, lịch sử lương vẫn hiển thị bình thường

### 4. Test Chức năng Xóa lịch sử lương - Xác nhận xóa
- [ ] 4.1: Đăng nhập vào hệ thống với user có quyền
- [ ] 4.2: Vào menu Nhân viên > chọn 1 nhân viên có lịch sử lương
- [ ] 4.3: Chuyển sang tab "Lương"
- [ ] 4.4: Ghi nhớ số lượng record lịch sử lương hiện tại
- [ ] 4.5: Click button "Xóa" trên 1 record lịch sử lương
- [ ] 4.6: Popup cảnh báo hiển thị với thông tin chi tiết record sẽ bị xóa
- [ ] 4.7: Click "Xác nhận"
- [ ] 4.8: Kiểm tra record lịch sử lương không còn hiển thị trong danh sách
- [ ] 4.9: Kiểm tra log activity được tạo trong chatter của nhân viên
- [ ] 4.10: Kiểm tra trong database record có active=False

### 5. Test Validation và Error Handling
- [ ] 5.1: Test validation "Từ ngày phải nhỏ hơn hoặc bằng Đến ngày"
- [ ] 5.2: Test các trường required không được để trống
- [ ] 5.3: Test permission access cho các wizard
- [ ] 5.4: Test validation trùng thời gian áp dụng
- [ ] 5.5: Test validation Đến ngày = Null chỉ cho lịch sử cuối cùng

### 6. Test Validation Trùng Thời Gian Áp Dụng
- [ ] 6.1: Tạo nhân viên có 2 lịch sử lương không trùng thời gian
- [ ] 6.2: Sửa lịch sử lương thứ nhất để trùng với lịch sử thứ hai
- [ ] 6.3: Click "Lưu"
- [ ] 6.4: Kiểm tra hiển thị lỗi: "Không thể sửa lịch sử lương do trùng thời gian áp dụng"
- [ ] 6.5: Popup đóng, không lưu thay đổi

### 7. Test Validation Đến Ngày = Null
- [ ] 7.1: Tạo nhân viên có nhiều lịch sử lương
- [ ] 7.2: Sửa lịch sử lương không phải cuối cùng, set Đến ngày = Null
- [ ] 7.3: Click "Lưu"
- [ ] 7.4: Kiểm tra hiển thị lỗi: "Chỉ được điều chỉnh Đến ngày = Null cho lịch sử lương cuối cùng"
- [ ] 7.5: Popup đóng, không lưu thay đổi
- [ ] 7.6: Sửa lịch sử lương cuối cùng, set Đến ngày = Null
- [ ] 7.7: Kiểm tra lưu thành công

### 8. Test Phụ Cấp Trong/Ngoài Lương
- [ ] 8.1: Mở wizard sửa lịch sử lương
- [ ] 8.2: Kiểm tra hiển thị section "Phụ cấp trong lương" và "Phụ cấp ngoài lương"
- [ ] 8.3: Thay đổi giá trị phụ cấp
- [ ] 8.4: Lưu và kiểm tra log activity ghi nhận thay đổi phụ cấp

## Implementation Summary

### Đã hoàn thành:
1. **Model hr.salary.history**:
   - Thêm trường `active` với default=True
   - Inherit mail.thread để có logging và tracking
   - Override method write để log changes chi tiết
   - Implement soft delete với method `soft_delete()`
   - Thêm tracking cho các trường quan trọng

2. **Wizard Edit Salary History**:
   - Tạo model `hr.salary.history.edit.wizard` với validation đầy đủ
   - Tạo model `hr.salary.history.edit.confirmation.wizard` cho confirmation dialog
   - Form view với đầy đủ các trường bao gồm phụ cấp trong/ngoài lương
   - Logic phân biệt edit mô tả vs edit các trường quan trọng
   - Confirmation dialog khi edit non-description fields
   - Validation trùng thời gian áp dụng với lịch sử lương khác
   - Validation Đến ngày = Null chỉ cho lịch sử lương cuối cùng

3. **Wizard Delete Salary History**:
   - Tạo model `hr.salary.history.delete.wizard`
   - Form view với thông tin chi tiết record sẽ bị xóa
   - Confirmation dialog với warning message

4. **Views Updates**:
   - Thêm button Edit và Delete vào tree view
   - Cập nhật domain để chỉ hiển thị active records
   - Cập nhật employee view để hiển thị buttons trong tab Lương

5. **Security & Access Rights**:
   - Cập nhật ir.model.access.csv cho tất cả wizard models
   - Đảm bảo terabit access như yêu cầu

6. **Payroll Integration**:
   - Cập nhật tất cả payroll calculations để chỉ sử dụng active salary history
   - Đảm bảo soft delete không ảnh hưởng đến tính toán lương

### Files đã tạo/sửa:
- `models/hr_salary_history.py` - Cập nhật model chính
- `wizard/hr_salary_history_edit_wizard.py` - Wizard edit
- `wizard/hr_salary_history_delete_wizard.py` - Wizard delete
- `wizard/hr_salary_history_edit_wizard_views.xml` - Views cho edit wizard
- `wizard/hr_salary_history_delete_wizard_views.xml` - Views cho delete wizard
- `views/hr_salary_history_views.xml` - Cập nhật tree view
- `views/hr_employee_views.xml` - Cập nhật employee view
- `security/ir.model.access.csv` - Thêm access rights
- `__manifest__.py` - Thêm view files
- `wizard/__init__.py` - Import wizard models
- `models/hr_payroll.py` - Cập nhật payroll calculations

## Cập nhật mới (theo yêu cầu bổ sung):

### ✅ Thêm Phụ cấp trong/ngoài lương:
- Thêm Properties fields `all_allowance_inside` và `all_allowance_outside` vào wizard edit
- Sử dụng PropertiesDefinition để định nghĩa structure
- Cập nhật form view để hiển thị 2 section phụ cấp với widget properties_custom
- Logging changes cho allowance fields trong write method

### ✅ Validation nâng cao:
- **Trùng thời gian áp dụng**: Kiểm tra overlap với lịch sử lương khác của cùng nhân viên
- **Đến ngày = Null**: Chỉ cho phép lịch sử lương cuối cùng có Đến ngày = Null
- Error messages rõ ràng theo đúng yêu cầu

### ✅ Logic xử lý cập nhật:
- Validation chạy trước khi hiển thị confirmation dialog
- Chỉ cho phép lưu khi không vi phạm business rules
- Bỏ các trường original, sử dụng write method để logging
- So sánh trực tiếp với salary_history_id để detect changes

## Notes
- ✅ Soft delete không ảnh hưởng đến payroll calculations (đã filter active=True)
- ✅ Logging rõ ràng về những thay đổi được thực hiện bao gồm allowance
- ✅ Confirmation dialogs user-friendly với warning messages
- ✅ Validation logic đầy đủ cho date range, overlap, và business rules
- ✅ Phụ cấp trong/ngoài lương được tích hợp đầy đủ
