from odoo import api, fields, models
from odoo.exceptions import ValidationError


class HrSalaryHistory(models.Model):
    _name = 'hr.salary.history'
    _description = '<PERSON><PERSON><PERSON> sử lương'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = "date_to desc"

    currency_id = fields.Many2one(comodel_name='res.currency', string="Tiền tệ", required=True,
                                  default=lambda self: self.env.company.currency_id)

    date_from = fields.Date(string='Từ ngày', required=True, tracking=True)
    date_to = fields.Date(string='Đến ngày', tracking=True)

    @api.constrains('date_from', 'date_to')
    def _check_date_range(self):
        for rec in self:
            if rec.date_from and rec.date_to and rec.date_from > rec.date_to:
                raise ValidationError('Từ ngày phải nhỏ hơn hoặc bằng Đến ngày')

    contract_salary = fields.Monetary(string='<PERSON>ứ<PERSON> lương theo hợp đồng', required=True, digits=(16, 0), tracking=True)

    base_salary = fields.Monetary(string='<PERSON><PERSON>ơng cơ bản', required=True, digits=(16, 0), tracking=True)

    effective_salary = fields.Monetary(string='Lương hiệu quả', required=True, digits=(16, 0), tracking=True)

    hr_employee_id = fields.Many2one(comodel_name='hr.employee', string='Nhân viên', required=True)

    description = fields.Text(string='Mô tả', tracking=True)

    active = fields.Boolean(string='Active', default=True, tracking=True)

    hr_level_id = fields.Many2one(related='hr_employee_id.hr_level_id', string='Cấp bậc')
    job_id = fields.Many2one(related='hr_employee_id.job_id', string='Vị trí công việc')

    all_allowance_inside = fields.Properties(
        string='Phụ cấp trong lương',
        definition_record='hr_employee_id',
        definition_record_field='all_properties_definition_allowance_inside',
        copy=True
    )
    all_allowance_outside = fields.Properties(
        string='Phụ cấp ngoài lương',
        definition_record='hr_employee_id',
        definition_record_field='all_properties_definition_allowance_outside',
        copy=True
    )

    def action_edit_salary_history(self):
        """Mở wizard để chỉnh sửa lịch sử lương"""
        self.ensure_one()
        view_id = self.env.ref('welly_salary.view_hr_salary_history_edit_wizard_form').id
        return {
            'name': 'Chỉnh sửa lịch sử lương',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'hr.salary.history.edit.wizard',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {
                'default_salary_history_id': self.id,
                'default_contract_salary': self.contract_salary,
                'default_base_salary': self.base_salary,
                'default_effective_salary': self.effective_salary,
                'default_date_from': self.date_from,
                'default_date_to': self.date_to,
                'default_description': self.description,
                'default_currency_id': self.currency_id.id,
            }
        }

    def action_delete_salary_history(self):
        """Mở wizard xác nhận xóa lịch sử lương"""
        self.ensure_one()
        view_id = self.env.ref('welly_salary.view_hr_salary_history_delete_wizard_form').id
        return {
            'name': 'Xóa lịch sử lương',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'hr.salary.history.delete.wizard',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {
                'default_salary_history_id': self.id,
            }
        }

    def soft_delete(self):
        for record in self:
            record.write({'active': False})
            record._log_salary_history_activity(
                f"Xóa lịch sử lương từ ngày {record.date_from} đến ngày {record.date_to or 'hiện tại'}"
            )

    def _log_salary_history_activity(self, message):
        """Log activity cho lịch sử lương"""
        self.env['mail.message'].create({
            'model': 'hr.employee',
            'res_id': self.hr_employee_id.id,
            'message_type': 'comment',
            'body': f"<strong>Lịch sử lương:</strong> {message}",
        })

    def write(self, vals):
        """Override write để log changes"""
        # Lưu giá trị cũ để so sánh
        old_values = {}
        for record in self:
            old_values[record.id] = {
                'contract_salary': record.contract_salary,
                'base_salary': record.base_salary,
                'effective_salary': record.effective_salary,
                'date_from': record.date_from,
                'date_to': record.date_to,
                'description': record.description,
                'all_allowance_inside': record.all_allowance_inside,
                'all_allowance_outside': record.all_allowance_outside,
            }

        # Thực hiện write
        result = super().write(vals)

        # Log changes nếu có
        for record in self:
            old_vals = old_values[record.id]
            changes = []
            if 'contract_salary' in vals and vals['contract_salary'] != old_vals['contract_salary']:
                changes.append(f"Lương hợp đồng: {old_vals['contract_salary']:,.0f} → {vals['contract_salary']:,.0f}")
            if 'base_salary' in vals and vals['base_salary'] != old_vals['base_salary']:
                changes.append(f"Lương cơ bản: {old_vals['base_salary']:,.0f} → {vals['base_salary']:,.0f}")
            if 'effective_salary' in vals and vals['effective_salary'] != old_vals['effective_salary']:
                changes.append(f"Lương hiệu quả: {old_vals['effective_salary']:,.0f} → {vals['effective_salary']:,.0f}")
            if 'date_from' in vals and vals['date_from'] != old_vals['date_from']:
                changes.append(f"Từ ngày: {old_vals['date_from']} → {vals['date_from']}")
            if 'date_to' in vals and vals['date_to'] != old_vals['date_to']:
                changes.append(f"Đến ngày: {old_vals['date_to'] or 'Không có'} → {vals['date_to'] or 'Không có'}")
            if 'description' in vals and vals['description'] != old_vals['description']:
                changes.append("Mô tả đã được cập nhật")
            if 'all_allowance_inside' in vals and vals['all_allowance_inside'] != old_vals['all_allowance_inside']:
                changes.append("Phụ cấp trong lương đã được cập nhật")
            if 'all_allowance_outside' in vals and vals['all_allowance_outside'] != old_vals['all_allowance_outside']:
                changes.append("Phụ cấp ngoài lương đã được cập nhật")
            if 'active' in vals and not vals['active']:
                changes.append("Lịch sử lương đã bị xóa")

            if changes:
                change_message = "Cập nhật lịch sử lương: " + "; ".join(changes)
                record._log_salary_history_activity(change_message)

        return result