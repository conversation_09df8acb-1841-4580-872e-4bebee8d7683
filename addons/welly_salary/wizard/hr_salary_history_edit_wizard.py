from odoo import api, fields, models
from odoo.exceptions import UserError
from datetime import timedelta


class HrSalaryHistoryEditWizard(models.TransientModel):
    _name = 'hr.salary.history.edit.wizard'
    _description = '<PERSON> chỉnh sửa lịch sử lương'

    salary_history_id = fields.Many2one('hr.salary.history', string='Lịch sử lương', required=True)
    
    currency_id = fields.Many2one(
        comodel_name='res.currency', 
        string="Tiền tệ", 
        required=True,
        default=lambda self: self.env.company.currency_id
    )

    date_from = fields.Date(string='Từ ngày', required=True)
    date_to = fields.Date(string='Đến ngày')

    contract_salary = fields.Monetary(string='<PERSON>ứ<PERSON> lương theo hợp đồng', required=True, digits=(16, 0))
    base_salary = fields.Monetary(string='Lương cơ bản', required=True, digits=(16, 0))
    effective_salary = fields.Monetary(string='<PERSON><PERSON>ơng hiệu quả', required=True, digits=(16, 0))
    description = fields.Text(string='<PERSON>ô tả')

    # <PERSON>ụ cấp
    all_properties_definition_allowance_inside = fields.PropertiesDefinition(
        string='Định nghĩa phụ cấp trong lương'
    )
    all_properties_definition_allowance_outside = fields.PropertiesDefinition(
        string='Định nghĩa phụ cấp ngoài lương'
    )
    all_allowance_inside = fields.Properties(
        string='Phụ cấp trong lương',
        definition_record='id',
        definition_record_field='all_properties_definition_allowance_inside',
        copy=True
    )
    all_allowance_outside = fields.Properties(
        string='Phụ cấp ngoài lương',
        definition_record='id',
        definition_record_field='all_properties_definition_allowance_outside',
        copy=True
    )



    @api.model
    def default_get(self, fields_list):
        """Set default values từ salary history record"""
        res = super().default_get(fields_list)
        if self.env.context.get('default_salary_history_id'):
            salary_history = self.env['hr.salary.history'].browse(
                self.env.context.get('default_salary_history_id')
            )
            employee = salary_history.hr_employee_id

            # Set definition trước
            res.update({
                'all_properties_definition_allowance_inside': employee.all_properties_definition_allowance_inside,
                'all_properties_definition_allowance_outside': employee.all_properties_definition_allowance_outside,
            })

            # Sau đó set values
            res.update({
                'all_allowance_inside': salary_history.all_allowance_inside,
                'all_allowance_outside': salary_history.all_allowance_outside,
            })
        return res

    @api.constrains('date_from', 'date_to')
    def _check_date_range(self):
        for rec in self:
            if rec.date_from and rec.date_to and rec.date_from > rec.date_to:
                raise UserError('Từ ngày phải nhỏ hơn hoặc bằng Đến ngày')

    def _check_date_overlap(self):
        """Kiểm tra trùng thời gian áp dụng với lịch sử lương khác"""
        self.ensure_one()
        employee = self.salary_history_id.hr_employee_id

        # Tìm các lịch sử lương khác của cùng nhân viên (trừ record hiện tại)
        other_histories = employee.salary_history_ids.filtered(
            lambda h: h.active and h.id != self.salary_history_id.id
        )

        for history in other_histories:
            # Kiểm tra overlap
            if self._periods_overlap(
                (self.date_from, self.date_to),
                (history.date_from, history.date_to)
            ):
                return True
        return False

    def _periods_overlap(self, period1, period2):
        """Kiểm tra 2 khoảng thời gian có overlap không"""
        start1, end1 = period1
        start2, end2 = period2

        # Nếu end date là None, coi như vô hạn
        if end1 is None:
            end1 = start1 + timedelta(days=36500)  # 100 years
        if end2 is None:
            end2 = start2 + timedelta(days=36500)

        return start1 <= end2 and start2 <= end1

    def _check_null_end_date_validation(self):
        """Kiểm tra Đến ngày = Null chỉ cho lịch sử lương cuối cùng"""
        self.ensure_one()
        if self.date_to is None:
            employee = self.salary_history_id.hr_employee_id

            # Tìm lịch sử lương cuối cùng (theo date_to null, rồi date_to lớn nhất)
            latest_history = employee.salary_history_ids.filtered(
                lambda h: h.active and h.id != self.salary_history_id.id
            ).sorted(key=lambda h: (not h.date_to, h.date_to), reverse=True)

            if latest_history and latest_history[0].id != self.salary_history_id.id:
                return True
        return False

    def _check_non_description_changes(self):
        """Kiểm tra xem có thay đổi nào khác ngoài mô tả không"""
        self.ensure_one()
        salary_history = self.salary_history_id
        return (
            self.contract_salary != salary_history.contract_salary or
            self.base_salary != salary_history.base_salary or
            self.effective_salary != salary_history.effective_salary or
            self.date_from != salary_history.date_from or
            self.date_to != salary_history.date_to or
            self.all_allowance_inside != salary_history.all_allowance_inside or
            self.all_allowance_outside != salary_history.all_allowance_outside
        )

    def action_save(self):
        """Lưu thay đổi lịch sử lương"""
        self.ensure_one()

        # Kiểm tra validation trước
        if self._check_date_overlap():
            raise UserError('Không thể sửa lịch sử lương do trùng thời gian áp dụng')

        if self._check_null_end_date_validation():
            raise UserError('Chỉ được điều chỉnh Đến ngày = Null cho lịch sử lương cuối cùng')

        # Kiểm tra xem có thay đổi nào khác ngoài mô tả không
        has_non_description_changes = self._check_non_description_changes()

        if has_non_description_changes:
            # Nếu có thay đổi khác ngoài mô tả, hiển thị confirmation dialog
            return self._show_confirmation_dialog()
        else:
            # Chỉ thay đổi mô tả, lưu trực tiếp
            return self._save_changes()

    def _show_confirmation_dialog(self):
        """Hiển thị dialog xác nhận khi có thay đổi quan trọng"""
        view_id = self.env.ref('welly_salary.view_hr_salary_history_edit_confirmation_wizard_form').id
        return {
            'name': 'Xác nhận chỉnh sửa lương',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'hr.salary.history.edit.confirmation.wizard',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {
                'default_edit_wizard_id': self.id,
            }
        }

    def _save_changes(self):
        """Thực hiện lưu thay đổi"""
        self.ensure_one()
        
        # Cập nhật salary history
        vals = {
            'contract_salary': self.contract_salary,
            'base_salary': self.base_salary,
            'effective_salary': self.effective_salary,
            'date_from': self.date_from,
            'date_to': self.date_to,
            'description': self.description,
            'all_allowance_inside': self.all_allowance_inside,
            'all_allowance_outside': self.all_allowance_outside,
        }
        
        # Cập nhật salary history - logging sẽ được xử lý trong write method
        self.salary_history_id.write(vals)
        
        return {'type': 'ir.actions.act_window_close'}

    def action_cancel(self):
        """Hủy thao tác"""
        return {'type': 'ir.actions.act_window_close'}


class HrSalaryHistoryEditConfirmationWizard(models.TransientModel):
    _name = 'hr.salary.history.edit.confirmation.wizard'
    _description = 'Wizard xác nhận chỉnh sửa lịch sử lương'

    edit_wizard_id = fields.Many2one('hr.salary.history.edit.wizard', string='Edit Wizard', required=True)
    
    def action_confirm(self):
        """Xác nhận và lưu thay đổi"""
        self.ensure_one()
        self.edit_wizard_id._save_changes()
        return {'type': 'ir.actions.act_window_close'}

    def action_cancel(self):
        """Hủy thao tác"""
        return {'type': 'ir.actions.act_window_close'}
