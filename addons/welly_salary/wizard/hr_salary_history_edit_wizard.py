from odoo import api, fields, models
from odoo.exceptions import UserError
from datetime import timedelta


class HrSalaryHistoryEditWizard(models.TransientModel):
    _name = 'hr.salary.history.edit.wizard'
    _description = '<PERSON> chỉnh sửa lịch sử lương'

    salary_history_id = fields.Many2one('hr.salary.history', string='Lịch sử lương', required=True)
    
    currency_id = fields.Many2one(
        comodel_name='res.currency', 
        string="Tiền tệ", 
        required=True,
        default=lambda self: self.env.company.currency_id
    )

    date_from = fields.Date(string='Từ ngày', required=True)
    date_to = fields.Date(string='Đến ngày')

    contract_salary = fields.Monetary(string='<PERSON><PERSON><PERSON> lương theo hợp đồng', required=True, digits=(16, 0))
    base_salary = fields.Monetary(string='Lương cơ bản', required=True, digits=(16, 0))
    effective_salary = fields.Monetary(string='<PERSON><PERSON>ơng hiệu quả', required=True, digits=(16, 0))
    description = fields.Text(string='<PERSON>ô tả')

    # <PERSON>ụ cấp
    all_properties_definition_allowance_inside = fields.PropertiesDefinition(
        string='Định nghĩa phụ cấp trong lương',
        related='salary_history_id.hr_employee_id.all_properties_definition_allowance_inside'
    )
    all_properties_definition_allowance_outside = fields.PropertiesDefinition(
        string='Định nghĩa phụ cấp ngoài lương',
        related='all_properties_definition_allowance_inside'
    )
    all_allowance_inside = fields.Properties(
        string='Phụ cấp trong lương',
        definition_record='salary_history_id',
        definition_record_field='all_properties_definition_allowance_inside',
        copy=True
    )
    all_allowance_outside = fields.Properties(
        string='Phụ cấp ngoài lương',
        definition_record='salary_history_id',
        definition_record_field='all_properties_definition_allowance_outside',
        copy=True
    )

    # Trường để track thay đổi
    original_contract_salary = fields.Monetary(string='Lương hợp đồng gốc', readonly=True)
    original_base_salary = fields.Monetary(string='Lương cơ bản gốc', readonly=True)
    original_effective_salary = fields.Monetary(string='Lương hiệu quả gốc', readonly=True)
    original_date_from = fields.Date(string='Từ ngày gốc', readonly=True)
    original_date_to = fields.Date(string='Đến ngày gốc', readonly=True)
    original_description = fields.Text(string='Mô tả gốc', readonly=True)
    original_all_allowance_inside = fields.Properties(string='Phụ cấp trong lương gốc', readonly=True)
    original_all_allowance_outside = fields.Properties(string='Phụ cấp ngoài lương gốc', readonly=True)

    @api.model
    def default_get(self, fields_list):
        """Set default values từ salary history record"""
        res = super().default_get(fields_list)
        if self.env.context.get('default_salary_history_id'):
            salary_history = self.env['hr.salary.history'].browse(
                self.env.context.get('default_salary_history_id')
            )
            res.update({
                'original_contract_salary': salary_history.contract_salary,
                'original_base_salary': salary_history.base_salary,
                'original_effective_salary': salary_history.effective_salary,
                'original_date_from': salary_history.date_from,
                'original_date_to': salary_history.date_to,
                'original_description': salary_history.description,
                'original_all_allowance_inside': salary_history.all_allowance_inside,
                'original_all_allowance_outside': salary_history.all_allowance_outside,
            })
        return res

    @api.constrains('date_from', 'date_to')
    def _check_date_range(self):
        for rec in self:
            if rec.date_from and rec.date_to and rec.date_from > rec.date_to:
                raise UserError('Từ ngày phải nhỏ hơn hoặc bằng Đến ngày')

    def _check_date_overlap(self):
        """Kiểm tra trùng thời gian áp dụng với lịch sử lương khác"""
        self.ensure_one()
        employee = self.salary_history_id.hr_employee_id

        # Tìm các lịch sử lương khác của cùng nhân viên (trừ record hiện tại)
        other_histories = employee.salary_history_ids.filtered(
            lambda h: h.active and h.id != self.salary_history_id.id
        )

        for history in other_histories:
            # Kiểm tra overlap
            if self._periods_overlap(
                (self.date_from, self.date_to),
                (history.date_from, history.date_to)
            ):
                return True
        return False

    def _periods_overlap(self, period1, period2):
        """Kiểm tra 2 khoảng thời gian có overlap không"""
        start1, end1 = period1
        start2, end2 = period2

        # Nếu end date là None, coi như vô hạn
        if end1 is None:
            end1 = start1 + timedelta(days=36500)  # 100 years
        if end2 is None:
            end2 = start2 + timedelta(days=36500)

        return start1 <= end2 and start2 <= end1

    def _check_null_end_date_validation(self):
        """Kiểm tra Đến ngày = Null chỉ cho lịch sử lương cuối cùng"""
        self.ensure_one()
        if self.date_to is None:
            employee = self.salary_history_id.hr_employee_id

            # Tìm lịch sử lương cuối cùng (theo date_from)
            latest_history = employee.salary_history_ids.filtered('active').sorted(
                key=lambda h: h.date_from, reverse=True
            )

            if latest_history and latest_history[0].id != self.salary_history_id.id:
                return True
        return False

    def _check_non_description_changes(self):
        """Kiểm tra xem có thay đổi nào khác ngoài mô tả không"""
        self.ensure_one()
        return (
            self.contract_salary != self.original_contract_salary or
            self.base_salary != self.original_base_salary or
            self.effective_salary != self.original_effective_salary or
            self.date_from != self.original_date_from or
            self.date_to != self.original_date_to or
            self.all_allowance_inside != self.original_all_allowance_inside or
            self.all_allowance_outside != self.original_all_allowance_outside
        )

    def action_save(self):
        """Lưu thay đổi lịch sử lương"""
        self.ensure_one()

        # Kiểm tra validation trước
        if self._check_date_overlap():
            raise UserError('Không thể sửa lịch sử lương do trùng thời gian áp dụng')

        if self._check_null_end_date_validation():
            raise UserError('Chỉ được điều chỉnh Đến ngày = Null cho lịch sử lương cuối cùng')

        # Kiểm tra xem có thay đổi nào khác ngoài mô tả không
        has_non_description_changes = self._check_non_description_changes()

        if has_non_description_changes:
            # Nếu có thay đổi khác ngoài mô tả, hiển thị confirmation dialog
            return self._show_confirmation_dialog()
        else:
            # Chỉ thay đổi mô tả, lưu trực tiếp
            return self._save_changes()

    def _show_confirmation_dialog(self):
        """Hiển thị dialog xác nhận khi có thay đổi quan trọng"""
        view_id = self.env.ref('welly_salary.view_hr_salary_history_edit_confirmation_wizard_form').id
        return {
            'name': 'Xác nhận chỉnh sửa lương',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'hr.salary.history.edit.confirmation.wizard',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {
                'default_edit_wizard_id': self.id,
            }
        }

    def _save_changes(self):
        """Thực hiện lưu thay đổi"""
        self.ensure_one()
        
        # Cập nhật salary history
        vals = {
            'contract_salary': self.contract_salary,
            'base_salary': self.base_salary,
            'effective_salary': self.effective_salary,
            'date_from': self.date_from,
            'date_to': self.date_to,
            'description': self.description,
            'all_allowance_inside': self.all_allowance_inside,
            'all_allowance_outside': self.all_allowance_outside,
        }
        
        self.salary_history_id.write(vals)
        
        # Log thay đổi
        changes = []
        if self.contract_salary != self.original_contract_salary:
            changes.append(f"Lương hợp đồng: {self.original_contract_salary:,.0f} → {self.contract_salary:,.0f}")
        if self.base_salary != self.original_base_salary:
            changes.append(f"Lương cơ bản: {self.original_base_salary:,.0f} → {self.base_salary:,.0f}")
        if self.effective_salary != self.original_effective_salary:
            changes.append(f"Lương hiệu quả: {self.original_effective_salary:,.0f} → {self.effective_salary:,.0f}")
        if self.date_from != self.original_date_from:
            changes.append(f"Từ ngày: {self.original_date_from} → {self.date_from}")
        if self.date_to != self.original_date_to:
            changes.append(f"Đến ngày: {self.original_date_to or 'Không có'} → {self.date_to or 'Không có'}")
        if self.description != self.original_description:
            changes.append("Mô tả đã được cập nhật")
        if self.all_allowance_inside != self.original_all_allowance_inside:
            changes.append("Phụ cấp trong lương đã được cập nhật")
        if self.all_allowance_outside != self.original_all_allowance_outside:
            changes.append("Phụ cấp ngoài lương đã được cập nhật")
        
        if changes:
            change_message = "Chỉnh sửa lịch sử lương: " + "; ".join(changes)
            self.salary_history_id._log_salary_history_activity(change_message)
        
        return {'type': 'ir.actions.act_window_close'}

    def action_cancel(self):
        """Hủy thao tác"""
        return {'type': 'ir.actions.act_window_close'}


class HrSalaryHistoryEditConfirmationWizard(models.TransientModel):
    _name = 'hr.salary.history.edit.confirmation.wizard'
    _description = 'Wizard xác nhận chỉnh sửa lịch sử lương'

    edit_wizard_id = fields.Many2one('hr.salary.history.edit.wizard', string='Edit Wizard', required=True)
    
    def action_confirm(self):
        """Xác nhận và lưu thay đổi"""
        self.ensure_one()
        self.edit_wizard_id._save_changes()
        return {'type': 'ir.actions.act_window_close'}

    def action_cancel(self):
        """Hủy thao tác"""
        return {'type': 'ir.actions.act_window_close'}
