<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Edit Wizard Form View -->
    <record id="view_hr_salary_history_edit_wizard_form" model="ir.ui.view">
        <field name="name">hr.salary.history.edit.wizard.form</field>
        <field name="model">hr.salary.history.edit.wizard</field>
        <field name="arch" type="xml">
            <form string="Chỉnh sửa lịch sử lương">
                <group>
                    <group>
                        <field name="currency_id" invisible="1"/>
                        <field name="salary_history_id" invisible="1"/>


                        <field name="contract_salary" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        <field name="effective_salary" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                    </group>
                    <group>
                        <field name="base_salary" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        <label for="date_from" string="<PERSON><PERSON><PERSON> t<PERSON>"/>
                        <div class="o_row">
                            <field name="date_from" widget="daterange" nolabel="1" class="oe_inline"
                                   options="{'related_end_date': 'date_to'}"/>
                            <span class="oe_inline"> đến </span>
                            <field name="date_to" widget="daterange" nolabel="1" class="oe_inline"
                                   options="{'related_start_date': 'date_from'}"/>
                        </div>
                    </group>
                </group>
                <group string="Phụ cấp trong lương" name="allowance_inside">
                    <field name="all_allowance_inside" widget="properties_custom" nolabel="1" colspan="2"/>
                </group>
                <group string="Phụ cấp ngoài lương" name="allowance_outside">
                    <field name="all_allowance_outside" widget="properties_custom" nolabel="1" colspan="2"/>
                </group>
                <group>
                    <field name="description" placeholder="Nhập mô tả..."/>
                </group>
                <footer>
                    <button string="Lưu" name="action_save" type="object" class="oe_highlight" data-hotkey="q"/>
                    <button string="Hủy" name="action_cancel" type="object" class="btn btn-default" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Confirmation Wizard Form View -->
    <record id="view_hr_salary_history_edit_confirmation_wizard_form" model="ir.ui.view">
        <field name="name">hr.salary.history.edit.confirmation.wizard.form</field>
        <field name="model">hr.salary.history.edit.confirmation.wizard</field>
        <field name="arch" type="xml">
            <form string="Xác nhận chỉnh sửa lương">
                <field name="edit_wizard_id" invisible="1"/>
                <strong>Chỉnh sửa lương sẽ không làm thay đổi các phiếu lương đã xác nhận. Bạn có muốn tiếp tục?</strong>
                <footer>
                    <button string="Xác nhận" name="action_confirm" type="object" class="oe_highlight" data-hotkey="q"/>
                    <button string="Hủy" name="action_cancel" type="object" class="btn btn-default" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
