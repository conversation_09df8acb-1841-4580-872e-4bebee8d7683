from datetime import datetime
import logging

_logger = logging.getLogger(__name__)


def migrate(cr, version):
    if version == '16.0.1.0':
        # update quyển chứng từ theo loại thanh toán trên biên nhận
        _update_account_move_line_in_account_payment(cr)
        # update diễn g<PERSON>h toán
        _update_payment_missing_line(cr)


def _update_account_move_line_in_account_payment(cr):
    cr.execute("SELECT id, move_id FROM account_payment")
    all_payments = cr.dictfetchall()  # Sử dụng cr.dictfetchall() sau cr.execute()

    for payment in all_payments:
        cr.execute("SELECT id, journal_id, account_id FROM account_move_line WHERE account_id != 12 AND move_id = %s",
                   (payment['move_id'],))
        move_lines = cr.dictfetchall()  # Sử dụng cr.dictfetchall() để lấy tất cả các dòng kết quả

        for line in move_lines:
            cr.execute("SELECT id, default_account_id FROM account_journal WHERE id = %s",
                       (line['journal_id'],))
            journal_id = cr.dictfetchone()  # Sử dụng cr.fetchone() để lấy kết quả duy nhất

            if journal_id and journal_id['default_account_id'] != line['account_id']:
                cr.execute("UPDATE account_move_line SET account_id = %s WHERE id = %s",
                           (journal_id['default_account_id'], line['id']))


def _update_payment_missing_line(cr):
    sql = """
        SELECT ap.id AS payment_id, wpl.id AS welly_line_id
        FROM account_payment ap
        INNER JOIN account_move am ON ap.id = am.payment_id
        INNER JOIN welly_payment_line wpl ON ap.id = wpl.payment_id
        WHERE ap.type = 'normal'
        AND am.state = 'posted'
    """
    cr.execute(sql)
    records = cr.dictfetchall()

    for r in records:
        payment_id = r['payment_id']
        welly_line_id = r['welly_line_id']

        # Lấy thông tin từ account.payment dựa trên payment_id
        cr.execute("""
                SELECT amount 
                FROM account_payment 
                WHERE id = %s
            """, (payment_id,))
        payment_data = cr.fetchone()

        if payment_data:
            amount_payment = payment_data[0]

            # Lấy welly_account_move_id từ welly_payment_line
            cr.execute("""
                    SELECT welly_account_move_id 
                    FROM welly_payment_line 
                    WHERE id = %s
                """, (welly_line_id,))
            welly_line = cr.fetchone()

            if welly_line:
                welly_account_move_id = welly_line[0]

                # Lấy account_move
                cr.execute("""
                        SELECT id, name, state, amount_total, amount_residual
                        FROM account_move 
                        WHERE id = %s
                    """, (welly_account_move_id,))
                account_move = cr.fetchone()
                account_move_id = account_move[0]
                account_move_name = account_move[1]
                account_move_state = account_move[2]
                account_move_amount_total = account_move[3]
                account_move_amount_residual = account_move[4]

                # Lấy thời gian hiện tại
                current_time = datetime.now()

                # Tạo dòng account.payment.invoice.line mới với các giá trị mặc định
                cr.execute("""
                    INSERT INTO account_payment_invoice_line 
                    (payment_id, "select", move_id, amount_payment, description, move_state, amount_total, amount_residual,  create_uid, write_uid, create_date, write_date) 
                    VALUES 
                    (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    payment_id,
                    True,
                    account_move_id,
                    amount_payment,
                    'Thanh toán cho %s' % account_move_name,
                    account_move_state,
                    account_move_amount_total,
                    account_move_amount_residual,
                    1,  # create_uid
                    1,  # write_uid
                    current_time,  # create_date
                    current_time  # write_date
                ))

                # Cập nhật trường type của account_payment thành 'inv_auto'
                cr.execute("""
                        UPDATE account_payment 
                        SET type = 'inv_auto' 
                        WHERE id = %s
                    """, (payment_id,))
